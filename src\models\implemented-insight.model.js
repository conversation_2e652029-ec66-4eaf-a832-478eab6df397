/**
 * ImplementedInsight Model
 * Represents the many-to-many relationship between User and Insight for implementations
 */
const { Model, DataTypes } = require('sequelize');

class ImplementedInsight extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        insightId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'Insight',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
      },
      {
        sequelize,
        modelName: 'ImplementedInsight',
        tableName: 'ImplementedInsight',
        timestamps: true,
        indexes: [
          {
            unique: true,
            fields: ['userId', 'insightId'],
            name: 'implemented_insight_unique_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // No direct associations needed as this is a junction table
    // The associations are defined in the User and Insight models
  }
}

module.exports = ImplementedInsight;
