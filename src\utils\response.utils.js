/**
 * API Response Utility
 *
 * Provides standardized response formatting
 */

/**
 * API Response formatter
 */
const ApiResponse = {
  /**
   * Format a success response
   * @param {Object} res - Express response object
   * @param {string} message - Success message
   * @param {*} data - Response data
   * @param {number|Object} statusCodeOrMeta - HTTP status code (default: 200) or metadata
   */
  success: (res, message, data = null, statusCodeOrMeta = 200) => {
    let statusCode = 200;
    let meta = null;

    // Check if the fourth parameter is a number (status code) or an object (metadata)
    if (typeof statusCodeOrMeta === 'number') {
      statusCode = statusCodeOrMeta;
    } else if (statusCodeOrMeta && typeof statusCodeOrMeta === 'object') {
      meta = statusCodeOrMeta;
    }

    const response = {
      status: statusCode,
      message,
    };

    if (data !== null) {
      response.data = data;
    }

    if (meta !== null) {
      // Add each metadata property to the response
      Object.keys(meta).forEach((key) => {
        if (key !== 'statusCode') {
          // Skip statusCode as it's already included
          response[key] = meta[key];
        }
      });
    }

    return res.status(statusCode).json(response);
  },

  /**
   * Format a created response (201)
   * @param {Object} res - Express response object
   * @param {string} message - Success message
   * @param {*} data - Response data
   */
  created: (res, message, data = null) => {
    return ApiResponse.success(res, message, data, 201);
  },

  /**
   * Format an error response
   * @param {Object} res - Express response object
   * @param {string} message - Error message
   * @param {*} errors - Error details
   * @param {number} statusCode - HTTP status code (default: 400)
   */
  error: (res, message, errors = null, statusCode = 400) => {
    // Define response with proper type annotation
    const response = {
      status: statusCode,
      error: getErrorTitle(statusCode),
      message,
      errors: null, // Initialize with null
    };

    if (errors !== null) {
      response.errors = errors;
    }

    return res.status(statusCode).json(response);
  },
};

/**
 * Get a descriptive title for an error based on status code
 * @param {number} statusCode - HTTP status code
 * @returns {string} Error title
 */
function getErrorTitle(statusCode) {
  switch (statusCode) {
    case 400:
      return 'Bad Request';
    case 401:
      return 'Unauthorized';
    case 403:
      return 'Forbidden';
    case 404:
      return 'Not Found';
    case 409:
      return 'Conflict';
    case 422:
      return 'Validation Error';
    case 500:
      return 'Internal Server Error';
    default:
      return 'Error';
  }
}

module.exports = {
  ApiResponse,
};
