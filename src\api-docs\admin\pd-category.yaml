openapi: 3.0.0
info:
  title: WTD Platform PD Categories API
  version: 1.0.0
  description: API endpoints for managing PD categories

paths:
  /admin/pd-categories:
    get:
      tags:
        - PD Categories
      summary: List All PD Categories
      description: Get a list of all PD categories with pagination and optional search
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
        - $ref: '#/components/parameters/SearchParam'
      responses:
        '200':
          description: Successfully retrieved PD categories
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PdCategoryListResponse'
    post:
      tags:
        - PD Categories
      summary: Create PD Category
      description: Create a new PD category
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePdCategoryRequest'
      responses:
        '201':
          description: Successfully created PD category
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PdCategoryResponse'
        '409':
          description: A category with this name already exists

  /admin/pd-categories/{id}:
    get:
      tags:
        - PD Categories
      summary: Get PD Category by ID
      description: Get a specific PD category by its ID
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/PdCategoryIdParam'
      responses:
        '200':
          description: Successfully retrieved PD category
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PdCategoryResponse'
        '404':
          description: PD category not found
    put:
      tags:
        - PD Categories
      summary: Update PD Category
      description: Update an existing PD category
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/PdCategoryIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePdCategoryRequest'
      responses:
        '200':
          description: Successfully updated PD category
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PdCategoryResponse'
        '404':
          description: PD category not found
        '409':
          description: Another category with this name already exists
    delete:
      tags:
        - PD Categories
      summary: Delete PD Category
      description: Delete an existing PD category
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/PdCategoryIdParam'
      responses:
        '200':
          description: Successfully deleted PD category
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteResponse'
        '404':
          description: PD category not found

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  parameters:
    PageParam:
      name: page
      in: query
      schema:
        type: integer
        minimum: 1
        default: 1
      description: Page number (1-based)

    LimitParam:
      name: limit
      in: query
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 10
      description: Number of items per page

    SearchParam:
      name: search
      in: query
      schema:
        type: string
      description: Optional search term to filter categories by name

    PdCategoryIdParam:
      name: id
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: PD category ID

  schemas:
    PdCategory:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the PD category
        name:
          type: string
          description: Name of the PD category
        createdAt:
          type: string
          format: date-time
          description: Date and time when the category was created
        updatedAt:
          type: string
          format: date-time
          description: Date and time when the category was last updated
      required:
        - id
        - name
        - createdAt
        - updatedAt
      example:
        id: "123e4567-e89b-12d3-a456-************"
        name: "Student Engagement"
        createdAt: "2023-01-01T00:00:00.000Z"
        updatedAt: "2023-01-01T00:00:00.000Z"

    CreatePdCategoryRequest:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          example: "Student Engagement"

    UpdatePdCategoryRequest:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          example: "Updated Category Name"

    PdCategoryResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "PD category created successfully"
        data:
          $ref: '#/components/schemas/PdCategory'

    PdCategoryListResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/PdCategory'
        pagination:
          $ref: '#/components/schemas/Pagination'

    DeleteResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "PD category deleted successfully"

    Pagination:
      type: object
      properties:
        total:
          type: integer
          description: Total number of items
          example: 12
        page:
          type: integer
          description: Current page number
          example: 1
        limit:
          type: integer
          description: Number of items per page
          example: 10
        totalPages:
          type: integer
          description: Total number of pages
          example: 2
        hasNext:
          type: boolean
          description: Whether there is a next page
          example: true
        hasPrevious:
          type: boolean
          description: Whether there is a previous page
          example: false
