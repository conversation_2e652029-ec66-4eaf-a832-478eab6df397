/**
 * Bookmark Service
 *
 * Handles business logic for bookmarking insights
 */
const bookmarkedInsightRepository = require('@models/repositories/bookmarked-insight.repository');

/**
 * Bookmark Service
 */
const bookmarkService = {
  /**
   * Toggle bookmark status for an insight
   * @param {string} userId - User ID
   * @param {string} insightId - Insight ID
   * @returns {Promise<Object>} Object with isBookmarked status
   */
  toggleBookmark: async (userId, insightId) => {
    try {
      return await bookmarkedInsightRepository.toggleBookmark(
        userId,
        insightId
      );
    } catch (error) {
      throw error;
    }
  },

  /**
   * Get all bookmarked insights for a user
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @param {number} options.page - Page number
   * @param {number} options.limit - Items per page
   * @returns {Promise<Object>} Insights and pagination info
   */
  getBookmarkedInsights: async (userId, { page, limit }) => {
    try {
      return await bookmarkedInsightRepository.getBookmarkedInsights(userId, {
        page,
        limit,
      });
    } catch (error) {
      throw error;
    }
  },
};

module.exports = bookmarkService;
