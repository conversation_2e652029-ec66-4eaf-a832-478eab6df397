/**
 * User Auth Module
 *
 * This module handles user authentication-related functionality
 */
const express = require('express');
const router = express.Router();
const { validate } = require('@middlewares/validation.middleware');
const { authenticate } = require('@middlewares/auth.middleware');
const authController = require('./auth.controller');
const authValidation = require('./auth.validation');

/**
 * Register routes
 */
function registerRoutes() {
  // User registration endpoint
  router.post(
    '/register',
    validate(authValidation.register),
    authController.register
  );

  // User login endpoint
  router.post('/login', validate(authValidation.login), authController.login);

  // User profile endpoints
  router.get('/profile', authenticate, authController.getProfile);

  router.put(
    '/profile',
    authenticate,
    validate(authValidation.updateProfile),
    authController.updateProfile
  );

  return router;
}

// Export the router
module.exports = registerRoutes();
