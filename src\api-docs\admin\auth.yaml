openapi: 3.0.0
info:
  title: WTD Platform Admin Authentication API
  version: 1.0.0
  description: Authentication endpoints for WTD Platform admin users

paths:
  /admin/login:
    post:
      tags:
        - Admin Authentication
      summary: Admin Login
      description: Authenticate an admin user
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdminLoginRequest'
      responses:
        '200':
          description: Successfully authenticated
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdminLoginResponse'
        '401':
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '422':
          description: Validation error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidationErrorResponse'

  /admin/profile:
    get:
      tags:
        - Admin Authentication
      summary: Get Admin Profile
      description: Get the profile of the currently authenticated admin
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Successfully retrieved admin profile
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdminProfileResponse'
        '401':
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Admin not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  schemas:
    AdminLoginRequest:
      type: object
      required:
        - email
        - password
      properties:
        email:
          type: string
          format: email
          default: <EMAIL>
        password:
          type: string
          format: password
          default: Qwerty@123

    AdminLoginResponse:
      type: object
      properties:
        status:
          type: string
          example: "success"
        message:
          type: string
          example: "Admin login successful"
        data:
          type: object
          properties:
            id:
              type: string
              format: uuid
            email:
              type: string
              format: email
            role:
              type: string
              enum: [admin]
            token:
              type: string

    AdminProfileResponse:
      type: object
      properties:
        status:
          type: string
          example: "success"
        message:
          type: string
          example: "Admin profile retrieved successfully"
        data:
          $ref: '#/components/schemas/AdminUser'

    AdminUser:
      type: object
      properties:
        id:
          type: string
          format: uuid
        email:
          type: string
          format: email
        role:
          type: string
          enum: [admin]

    ErrorResponse:
      type: object
      properties:
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "Error message"
        errors:
          type: object
          nullable: true

    ValidationErrorResponse:
      type: object
      properties:
        status:
          type: string
          example: "error"
        message:
          type: string
          example: "Validation failed"
        errors:
          type: array
          items:
            type: object
            properties:
              param:
                type: string
                example: "email"
              msg:
                type: string
                example: "Valid email is required"
              location:
                type: string
                example: "body"
