openapi: 3.0.0
info:
  title: WTD Platform Focus Areas API
  version: 1.0.0
  description: API endpoints for managing Focus areas

paths:
  /admin/focus:
    get:
      tags:
        - Focus Areas
      summary: List All Focus Areas
      description: Get a list of all focus areas with pagination and optional search
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
        - $ref: '#/components/parameters/SearchParam'
      responses:
        '200':
          description: Successfully retrieved focus areas
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FocusListResponse'
    post:
      tags:
        - Focus Areas
      summary: Create Focus Area
      description: Create a new focus area
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateFocusRequest'
      responses:
        '201':
          description: Successfully created focus area
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FocusResponse'
        '409':
          description: A focus area with this name already exists

  /admin/focus/{id}:
    get:
      tags:
        - Focus Areas
      summary: Get Focus Area by ID
      description: Get a specific focus area by its ID
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/FocusIdParam'
      responses:
        '200':
          description: Successfully retrieved focus area
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FocusResponse'
        '404':
          description: Focus area not found
    put:
      tags:
        - Focus Areas
      summary: Update Focus Area
      description: Update an existing focus area
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/FocusIdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateFocusRequest'
      responses:
        '200':
          description: Successfully updated focus area
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FocusResponse'
        '404':
          description: Focus area not found
        '409':
          description: Another focus area with this name already exists
    delete:
      tags:
        - Focus Areas
      summary: Delete Focus Area
      description: Delete an existing focus area
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/FocusIdParam'
      responses:
        '200':
          description: Successfully deleted focus area
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteResponse'
        '404':
          description: Focus area not found

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  parameters:
    PageParam:
      name: page
      in: query
      schema:
        type: integer
        minimum: 1
        default: 1
      description: Page number (1-based)

    LimitParam:
      name: limit
      in: query
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 10
      description: Number of items per page

    SearchParam:
      name: search
      in: query
      schema:
        type: string
      description: Optional search term to filter focus areas by name

    FocusIdParam:
      name: id
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: Focus area ID

  schemas:
    Focus:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the focus area
        name:
          type: string
          description: Name of the focus area
        createdAt:
          type: string
          format: date-time
          description: Date and time when the focus area was created
        updatedAt:
          type: string
          format: date-time
          description: Date and time when the focus area was last updated
      required:
        - id
        - name
        - createdAt
        - updatedAt
      example:
        id: "123e4567-e89b-12d3-a456-************"
        name: "Elementary"
        createdAt: "2023-01-01T00:00:00.000Z"
        updatedAt: "2023-01-01T00:00:00.000Z"

    CreateFocusRequest:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          example: "Elementary"

    UpdateFocusRequest:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          example: "Updated Focus Name"

    FocusResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Focus created successfully"
        data:
          $ref: '#/components/schemas/Focus'

    FocusListResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/Focus'
        pagination:
          $ref: '#/components/schemas/Pagination'

    DeleteResponse:
      type: object
      properties:
        success:
          type: boolean
          example: true
        message:
          type: string
          example: "Focus deleted successfully"

    Pagination:
      type: object
      properties:
        total:
          type: integer
          description: Total number of items
          example: 3
        page:
          type: integer
          description: Current page number
          example: 1
        limit:
          type: integer
          description: Number of items per page
          example: 10
        totalPages:
          type: integer
          description: Total number of pages
          example: 1
        hasNext:
          type: boolean
          description: Whether there is a next page
          example: false
        hasPrevious:
          type: boolean
          description: Whether there is a previous page
          example: false