/**
 * Admin Insight Controller
 *
 * Handles insight-related HTTP requests for admins
 */
const insightService = require('./insight.service');
const { ApiResponse } = require('@utils/response.utils');
const { INSIGHT, REPORT } = require('@utils/messages.utils');
const { InsightStatus } = require('@utils/enums.utils');

/**
 * Admin insight controller
 */
const insightController = {
  /**
   * Get all insights with pagination and optional search
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getAllInsights: async (req, res, next) => {
    try {
      const { page = 1, limit = 10, search = '', status } = req.query;
      const result = await insightService.getAllInsights({
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        search,
        status,
      });

      return ApiResponse.success(
        res,
        INSIGHT.ALL_RETRIEVED,
        result.insights,
        result.pagination
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get insight by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getInsightById: async (req, res, next) => {
    try {
      const { id } = req.params;
      const insight = await insightService.getInsightById(id);

      return ApiResponse.success(res, INSIGHT.RETRIEVED, insight);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Update an insight
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  updateInsight: async (req, res, next) => {
    try {
      const { id } = req.params;

      // Destructure only the allowed fields
      const { insightText, sourceUrl, pdCategoryId, focusIds, wtdCategoryIds } =
        req.body;

      // Filter undefined values
      const updateData = {
        ...(insightText !== undefined && { insightText }),
        ...(sourceUrl !== undefined && { sourceUrl }),
        ...(pdCategoryId !== undefined && { pdCategoryId }),
        ...(focusIds !== undefined && { focusIds }),
        ...(wtdCategoryIds !== undefined && { wtdCategoryIds }),
      };

      const insight = await insightService.updateInsight(id, updateData);

      return ApiResponse.success(res, INSIGHT.UPDATED, insight);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Delete an insight
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  deleteInsight: async (req, res, next) => {
    try {
      const { id } = req.params;
      await insightService.deleteInsight(id);

      return ApiResponse.success(res, INSIGHT.DELETED);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Update insight status (approve or reject)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  updateStatus: async (req, res, next) => {
    try {
      const { id } = req.params;
      const { status, rejectionReason } = req.body;
      const adminId = req.user.id;

      const insight = await insightService.updateStatus(
        id,
        adminId,
        status,
        rejectionReason
      );

      // Return appropriate message based on status
      const message =
        status === InsightStatus.APPROVED ? INSIGHT.APPROVED : INSIGHT.REJECTED;

      return ApiResponse.success(res, message, insight);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get all insight reports
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getInsightReports: async (req, res, next) => {
    try {
      const { page = 1, limit = 10, search = '' } = req.query;
      const result = await insightService.getInsightReports({
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        search,
      });

      return ApiResponse.success(
        res,
        REPORT.REPORTS_RETRIEVED,
        result.reports,
        result.pagination
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get all contribution reports
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  getContributionReports: async (req, res, next) => {
    try {
      const { page = 1, limit = 10, search = '' } = req.query;
      const result = await insightService.getContributionReports({
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        search,
      });

      return ApiResponse.success(
        res,
        REPORT.REPORTS_RETRIEVED,
        result.reports,
        result.pagination
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Delete a contribution (admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next middleware function
   */
  deleteContribution: async (req, res, next) => {
    try {
      const { contributionId } = req.params;
      await insightService.deleteContribution(contributionId);

      return ApiResponse.success(res, 'Contribution deleted successfully');
    } catch (error) {
      next(error);
    }
  },
};

module.exports = insightController;
