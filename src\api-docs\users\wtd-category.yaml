openapi: 3.0.0
info:
  title: WTD Platform User WTD Categories API
  version: 1.0.0
  description: API endpoints for accessing WTD categories as a user

paths:
  /user/wtd-categories:
    get:
      tags:
        - User WTD Categories
      summary: List All WTD Categories
      description: Get a list of all WTD categories with pagination and optional search
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
        - $ref: '#/components/parameters/SearchParam'
      responses:
        '200':
          description: Successfully retrieved WTD categories
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WtdCategoryListResponse'
        '401':
          description: Unauthorized - User is not authenticated

  /user/wtd-categories/{id}:
    get:
      tags:
        - User WTD Categories
      summary: Get WTD Category by ID
      description: Get a specific WTD category by its ID
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/WtdCategoryIdParam'
      responses:
        '200':
          description: Successfully retrieved WTD category
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WtdCategoryResponse'
        '401':
          description: Unauthorized - User is not authenticated
        '404':
          description: WTD category not found

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  parameters:
    PageParam:
      name: page
      in: query
      schema:
        type: integer
        minimum: 1
        default: 1
      description: Page number (1-based)

    LimitParam:
      name: limit
      in: query
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 10
      description: Number of items per page

    SearchParam:
      name: search
      in: query
      schema:
        type: string
      description: Optional search term to filter WTD categories by name

    WtdCategoryIdParam:
      name: id
      in: path
      required: true
      schema:
        type: string
        format: uuid
      description: WTD category ID
  schemas:
    WtdCategory:
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the WTD category
        name:
          type: string
          description: Name of the WTD category
        createdAt:
          type: string
          format: date-time
          description: Date and time when the WTD category was created
        updatedAt:
          type: string
          format: date-time
          description: Date and time when the WTD category was last updated
      required:
        - id
        - name
        - createdAt
        - updatedAt
      example:
        id: "123e4567-e89b-12d3-a456-************"
        name: "Category 1"
        createdAt: "2023-01-01T00:00:00.000Z"
        updatedAt: "2023-01-01T00:00:00.000Z"

    WtdCategoryResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "WTD category retrieved successfully"
        data:
          $ref: '#/components/schemas/WtdCategory'

    WtdCategoryListResponse:
      type: object
      properties:
        status:
          type: integer
          example: 200
        message:
          type: string
          example: "All WTD categories retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/WtdCategory'
        pagination:
          $ref: '#/components/schemas/Pagination'

    Pagination:
      type: object
      properties:
        total:
          type: integer
          description: Total number of items
          example: 3
        page:
          type: integer
          description: Current page number
          example: 1
        limit:
          type: integer
          description: Number of items per page
          example: 10
        totalPages:
          type: integer
          description: Total number of pages
          example: 1
        hasNext:
          type: boolean
          description: Whether there is a next page
          example: false
        hasPrevious:
          type: boolean
          description: Whether there is a previous page
          example: false
