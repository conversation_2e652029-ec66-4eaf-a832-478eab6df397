'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Create a trigger to update user type when a membership is created
    await queryInterface.sequelize.query(`
      CREATE OR REPLACE FUNCTION update_user_type_on_membership()
      <PERSON><PERSON><PERSON><PERSON> trigger AS $$
      BEGIN
        -- Update the user's type based on the membership type
        -- Cast the membership type to text and then to the user type enum to avoid type mismatch
        UPDATE "User"
        SET "userType" = NEW."membershipType"::text::"enum_User_userType"
        WHERE id = NEW."userId";
        
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      DROP TRIGGER IF EXISTS membership_update_user_type ON "Membership";
      
      CREATE TRIGGER membership_update_user_type
      AFTER INSERT ON "Membership"
      FOR EACH ROW
      EXECUTE FUNCTION update_user_type_on_membership();
    `);

    // Create a trigger to handle membership expiration
    await queryInterface.sequelize.query(`
      CREATE OR REPLACE FUNCTION handle_membership_expiration()
      <PERSON><PERSON><PERSON><PERSON> trigger AS $$
      BEGIN
        -- If the membership is being deactivated, check if there are any other active memberships
        IF OLD."isActive" = TRUE AND NEW."isActive" = FALSE THEN
          -- If no other active memberships exist, revert user type to 'EDUCATOR'
          IF NOT EXISTS (
            SELECT 1 FROM "Membership" 
            WHERE "userId" = NEW."userId" 
            AND "isActive" = TRUE 
            AND id != NEW.id
          ) THEN
            UPDATE "User"
            SET "userType" = 'EDUCATOR'::"enum_User_userType"
            WHERE id = NEW."userId";
          END IF;
        END IF;
        
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      DROP TRIGGER IF EXISTS membership_handle_expiration ON "Membership";
      
      CREATE TRIGGER membership_handle_expiration
      AFTER UPDATE ON "Membership"
      FOR EACH ROW
      EXECUTE FUNCTION handle_membership_expiration();
    `);
  },

  async down(queryInterface, Sequelize) {
    // Drop the triggers and functions
    await queryInterface.sequelize.query(`
      DROP TRIGGER IF EXISTS membership_update_user_type ON "Membership";
      DROP FUNCTION IF EXISTS update_user_type_on_membership();
      
      DROP TRIGGER IF EXISTS membership_handle_expiration ON "Membership";
      DROP FUNCTION IF EXISTS handle_membership_expiration();
    `);
  },
};
