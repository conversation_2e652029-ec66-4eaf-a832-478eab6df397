/**
 * Experience Module
 *
 * Defines experience routes and their handlers
 */
const express = require('express');
const router = express.Router();

// Import middleware
const { authenticate } = require('@middlewares/auth.middleware');
const { requireProviderPlus } = require('@middlewares/role.middleware');
const { validate } = require('@middlewares/validation.middleware');

// Import controller and validation
const experienceController = require('./experience.controller');
const experienceValidation = require('./experience.validation');

/**
 * Experience routes
 */

// POST /user/experiences - Create a new experience
router.post(
  '/',
  authenticate,
  requireProviderPlus,
  validate(experienceValidation.create),
  experienceController.createExperience
);

// GET /user/experiences - Get all experiences with pagination and search
router.get(
  '/',
  authenticate,
  validate(experienceValidation.getAll),
  experienceController.getAllExperiences
);

// GET /user/experiences/:id - Get experience by ID
router.get(
  '/:id',
  authenticate,
  validate(experienceValidation.getById),
  experienceController.getExperienceById
);

module.exports = router;
