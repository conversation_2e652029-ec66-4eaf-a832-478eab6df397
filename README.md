# WTD Platform

WTD Platform API with proper architecture and essential features.

## Features

- **Modern JavaScript**: ES6+ syntax
- **Architecture**: Clean, modular architecture with separation of concerns
- **Authentication**: JWT-based authentication system
- **Database**: Sequelize ORM with PostgreSQL
- **Validation**: Request validation using express-validator
- **Error Handling**: Centralized error handling
- **Logging**: Structured logging
- **Security**: Implementation of security best practices
- **API Documentation**: Swagger/OpenAPI documentation
- **Environment Configuration**: Environment-based configuration
- **Code Quality**: ESLint and Prettier for code quality
- **Consistent Messaging**: Centralized message management for errors and success responses

## Project Structure

```
.
├── scripts/               # Helper scripts
│   └── run-dev.js         # Development server script
├── src/                   # Source code
│   ├── api-docs/          # API documentation (Swagger/OpenAPI)
│   │   ├── admin/         # Admin API documentation
│   │   └── users/         # User API documentation
│   ├── config/            # Configuration files
│   │   └── triggers/      # Database triggers
│   ├── middlewares/       # Express middlewares
│   ├── migrations/        # Database migrations
│   ├── models/            # Database models
│   │   └── repositories/  # Data access layer
│   ├── modules/           # Feature modules
│   │   ├── auth/          # Authentication module
│   │   └── users/         # Users module
│   ├── seeders/           # Database seeders
│   ├── utils/             # Utility functions
│   │   ├── messages.utils.js  # Centralized error and success messages
│   │   └── ...            # Other utility files
│   ├── app.js             # Express application
│   └── routes.js          # Route definitions
├── public/                # Static files
├── .env                   # Environment variables
├── .env.example           # Example environment variables
├── .gitignore             # Git ignore file
├── .sequelizerc           # Sequelize CLI configuration
├── main.js                # Main entry point
├── package.json           # Project dependencies
└── README.md              # Project documentation
```

## Requirements

For development, you will need Node.js and Yarn installed in your environment.

### Node.js and Yarn

- Node.js >= 20.12.1
- Yarn >= 1.22.22

You can download Node.js from the [official Node.js website](https://nodejs.org/) and Yarn from the [official Yarn website](https://yarnpkg.com/).

## Getting Started

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd wtd-platform
   ```

2. Install dependencies:
   ```bash
   yarn install
   ```

3. Create a `.env` file based on `.env.example`:
   ```bash
   cp .env.example .env
   ```

4. Update the `.env` file with your configuration.

5. Start the development server:
   ```bash
   yarn dev
   ```

## Database Setup

1. Create a PostgreSQL database.

2. Update the database configuration in `.env`:
   ```
   DATABASE_URL=postgres://username:password@localhost:5432/database_name
   ```

   The application automatically selects the correct database configuration based on the `NODE_ENV` environment variable:
   - `NODE_ENV=development` (default): Uses the development configuration
   - `NODE_ENV=production`: Uses the production configuration
   - `NODE_ENV=test`: Uses the test configuration
   - `NODE_ENV=local`: Uses the local configuration

3. Run the following commands in your PostgreSQL database:
   ```sql
   CREATE EXTENSION IF NOT EXISTS postgis;
   CREATE EXTENSION IF NOT EXISTS pgcrypto;
   ```

4. Run migrations:
   ```bash
   yarn db:migrate
   ```

5. Seed the database (optional):
   ```bash
   yarn db:seed
   ```

## API Documentation

API documentation is available at the following endpoints when running in development mode:

### Legacy API Documentation
- User API: http://localhost:3000/api-docs/user
- Admin API: http://localhost:3000/api-docs/admin

### New Swagger Documentation (with Basic Auth)
- User API: http://localhost:3000/swagger-docs/v1/user
- Admin API: http://localhost:3000/swagger-docs/v1/admin

Default credentials for Swagger documentation:
- Admin: username: `admin`, password: `admin123`
- User: username: `user`, password: `user123`

You can change these credentials in the `.env` file.

## Database Commands

### Migrations

Generate a new migration:
```bash
npx sequelize-cli migration:generate --name migration_name
```

Create a new migration:
```bash
npx sequelize-cli migration:create --name migration_name
```

Run migrations:
```bash
npx sequelize-cli db:migrate
# or
yarn db:migrate
```

Undo the last migration:
```bash
npx sequelize-cli db:migrate:undo
# or
yarn db:migrate:undo
```

### Seeds

Generate a new seed:
```bash
npx sequelize-cli seed:generate --name seed_name
```

Run seeds:
```bash
npx sequelize-cli db:seed:all
# or
yarn db:seed
```

Undo the last seed:
```bash
npx sequelize-cli db:seed:undo
# or
yarn db:seed:undo
```

## Centralized Messages

The application uses a centralized approach for managing error and success messages. All messages are stored in `src/utils/messages.utils.js` and organized by module/feature:

- `COMMON`: Common messages used across the application
- `AUTH`: Authentication-related messages
- `ADMIN`: Admin-related messages
- `USER`: User-related messages
- `VALIDATION`: Validation-related messages
- `UPLOAD`: File upload-related messages

### Usage Example

```javascript
// Import the messages
const { AUTH, ADMIN } = require('@utils/messages.utils');

// Use in API responses
return ApiResponse.success(res, ADMIN.LOGIN_SUCCESS, data);

// Use in error handling
throw new ApiException(401, AUTH.INVALID_CREDENTIALS);

// Use in validation
body('email').isEmail().withMessage(VALIDATION.EMAIL_INVALID);
```

## Error Handling

The application implements a layered error handling approach:

### Repository Layer

The repository layer uses try-catch blocks to handle database-related errors:

```javascript
async findById(id) {
  try {
    const admin = await Admin.findByPk(id);

    if (!admin) {
      throw new ApiException(404, ADMIN.ADMIN_NOT_FOUND);
    }

    return admin;
  } catch (error) {
    // Log the error for debugging
    console.error('Error in findById repository:', error);

    // Simply rethrow the error
    throw error;
  }
}
```

### Service Layer

The service layer also uses try-catch blocks to handle business logic errors:

```javascript
async getAdminProfile(adminId) {
  try {
    // Find admin
    const admin = await adminRepository.findById(adminId);

    // Return admin data (without password)
    return {
      id: admin.id,
      email: admin.email,
      role: 'admin',
    };
  } catch (error) {
    // Log the error for debugging
    console.error('Error in getAdminProfile service:', error);

    // Simply rethrow the error
    throw error;
  }
}
```

### Controller Layer

The controller layer catches all errors and passes them to the Express error handler:

```javascript
async getProfile(req, res, next) {
  try {
    const adminId = req.user.id;
    const admin = await adminService.getAdminProfile(adminId);
    return ApiResponse.success(res, ADMIN.PROFILE_RETRIEVED, admin);
  } catch (error) {
    next(error);
  }
}
```

### Global Error Handler

The global error handler middleware formats all errors into a consistent API response:

```javascript
const errorHandler = (err, req, res, next) => {
  Logger.error(`Error: ${err.message}`, err);

  // Handle ApiException
  if (err instanceof ApiException) {
    return ApiResponse.error(res, err.message, err.errors, err.statusCode);
  }

  // Handle Sequelize validation errors
  if (err.name === 'SequelizeValidationError') {
    return ApiResponse.error(
      res,
      'Validation error',
      err.errors.map((e) => ({ field: e.path, message: e.message })),
      400
    );
  }

  // Handle JWT errors
  if (err.name === 'JsonWebTokenError' || err.name === 'TokenExpiredError') {
    return ApiResponse.error(res, 'Authentication error', err.message, 401);
  }

  // Handle other errors
  const statusCode = err.statusCode || 500;
  const message = statusCode === 500 ? 'Internal server error' : err.message;

  return ApiResponse.error(res, message, null, statusCode);
};
```

## Scripts

- `yarn start`: Start the server
- `yarn dev`: Start the development server with automatic migrations and seeding
- `yarn start:prod`: Start the server in production mode
- `yarn start:full`: Run migrations, seed the database, and start the server
- `yarn lint`: Run ESLint
- `yarn prettier`: Run Prettier
- `yarn format`: Run both Prettier and ESLint
- `yarn db:migrate`: Run database migrations
- `yarn db:migrate:undo`: Undo the last migration
- `yarn db:migrate:generate`: Generate a new migration
- `yarn db:model:generate`: Generate a new model
- `yarn db:seed`: Run database seeders
- `yarn db:seed:undo`: Undo the last seeder
- `yarn db:seed:generate`: Generate a new seeder

