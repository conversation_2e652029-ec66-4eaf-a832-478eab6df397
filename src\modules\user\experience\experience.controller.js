/**
 * Experience Controller
 *
 * Handles experience-related HTTP requests
 */
const experienceService = require('./experience.service');
const { ApiResponse } = require('@utils/response.utils');
const { EXPERIENCE } = require('@utils/messages.utils');

/**
 * Experience controller
 */
const experienceController = {
  /**
   * Create a new experience
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  createExperience: async (req, res, next) => {
    try {
      const data = req.body;
      const userId = req.user.id; // Assuming user is attached from auth middleware

      const experience = await experienceService.createExperience(data, userId);

      return ApiResponse.created(res, EXPERIENCE.CREATED, experience);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get all experiences with pagination and optional search
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getAllExperiences: async (req, res, next) => {
    try {
      // Extract query parameters
      const { page = 1, limit = 10, search = '', myExperiences } = req.query;

      const options = {
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        search,
      };

      // If myExperiences is true, filter by current user
      if (myExperiences === 'true') {
        options.userId = req.user.id;
      }

      const result = await experienceService.getAllExperiences(options);

      return ApiResponse.success(
        res,
        EXPERIENCE.ALL_RETRIEVED,
        result.experiences,
        {
          pagination: result.pagination,
        }
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get experience by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getExperienceById: async (req, res, next) => {
    try {
      const { id } = req.params;

      const experience = await experienceService.getExperienceById(id);

      return ApiResponse.success(res, EXPERIENCE.RETRIEVED, experience);
    } catch (error) {
      next(error);
    }
  },
};

module.exports = experienceController;
