const path = require('path');
require('dotenv').config();

// Check if we're running in a cloud environment
const isCloudEnv = process.env.AWS_EXECUTION_ENV || process.env.EB_ENVIRONMENT || process.env.NODE_ENV === 'production';
console.log(`Sequelize CLI: Running in ${isCloudEnv ? 'cloud' : 'local'} environment`);

// Determine environment
const env = process.env.NODE_ENV || 'development';
const isLocal = env === 'local';

// Configure SSL based on environment
const sslConfig = isLocal
  ? false // Disable SSL for localhost
  : {     // Enable SSL for all other environments
      require: true,
      rejectUnauthorized: false // Allow self-signed certificates
    };

console.log(`Sequelize CLI: Using environment "${env}" with SSL ${isLocal ? 'disabled' : 'enabled'}`);

// Use DATABASE_URL from .env if available
const config = process.env.DATABASE_URL
  ? {
      "development": {
        "use_env_variable": "DATABASE_URL",
        "dialect": "postgres",
        "define": {
          "timestamps": true,
          "underscored": false
        },
        "dialectOptions": {
          "ssl": sslConfig
        }
      },
      "test": {
        "use_env_variable": "DATABASE_URL",
        "dialect": "postgres",
        "define": {
          "timestamps": true,
          "underscored": false
        },
        "dialectOptions": {
          "ssl": sslConfig
        }
      },
      "production": {
        "use_env_variable": "DATABASE_URL",
        "dialect": "postgres",
        "define": {
          "timestamps": true,
          "underscored": false
        },
        "dialectOptions": {
          "ssl": sslConfig
        }
      },
      "local": {
        "use_env_variable": "DATABASE_URL",
        "dialect": "postgres",
        "define": {
          "timestamps": true,
          "underscored": false
        },
        "dialectOptions": {
          "ssl": false
        }
      }
    }
  : null;

// Write config to a temporary file if using DATABASE_URL
if (config) {
  const fs = require('fs');
  const configPath = path.resolve('./src/config', 'config.json');
  fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
}

module.exports = {
    "config": path.resolve('./src/config', 'config.json'),
    "models-path": path.resolve('./src/models'),
    "seeders-path": path.resolve('./src/seeders'),
    "migrations-path": path.resolve('./src/migrations')
};
