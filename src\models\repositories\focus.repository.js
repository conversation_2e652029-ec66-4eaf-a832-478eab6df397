/**
 * Focus Repository
 *
 * Handles data access operations for the Focus model
 */
const { ApiException } = require('@utils/exception.utils');
const Focus = require('@models/focus.model');
const { FOCUS } = require('@utils/messages.utils');
const { Op } = require('sequelize');

class FocusRepository {
  /**
   * Find all focus with pagination and optional search
   * @param {Object} options - Query options
   * @param {number} options.page - Page number (1-based)
   * @param {number} options.limit - Number of items per page
   * @param {string} options.search - Optional search term for name
   * @returns {Promise<Object>} Object containing categories and pagination info
   */
  async findAll({ page = 1, limit = 10, search = '' } = {}) {
    try {
      // Build where clause for search
      const whereClause = {};
      if (search) {
        whereClause.name = {
          [Op.iLike]: `%${search}%`,
        };
      }

      // Calculate offset
      const offset = (page - 1) * limit;

      // Get total count
      const count = await Focus.count({ where: whereClause });

      // Get paginated results
      const focus = await Focus.findAll({
        where: whereClause,
        order: [['name', 'ASC']],
        limit,
        offset,
      });

      // Calculate pagination metadata
      const totalPages = Math.ceil(count / limit);
      const hasNext = page < totalPages;
      const hasPrevious = page > 1;

      return {
        focus,
        pagination: {
          total: count,
          page,
          limit,
          totalPages,
          hasNext,
          hasPrevious,
        },
      };
    } catch (error) {
      console.error('Error in findAll repository:', error);
      throw error;
    }
  }

  /**
   * Find Focus by ID
   * @param {string} id - Focus UUID
   * @returns {Promise<Focus>} Focus instance
   * @throws {ApiException} If Focus not found
   */
  async findById(id) {
    try {
      const focus = await Focus.findByPk(id);

      if (!focus) {
        throw new ApiException(404, FOCUS.NOT_FOUND);
      }

      return focus;
    } catch (error) {
      console.error('Error in findById repository:', error);
      throw error;
    }
  }

  /**
   * Find Focus by name (case-insensitive)
   * @param {string} name - Focus name
   * @returns {Promise<Focus|null>} Focus instance or null if not found
   */
  async findByName(name) {
    try {
      return await Focus.findOne({
        where: {
          name: {
            [Op.iLike]: name,
          },
        },
      });
    } catch (error) {
      console.error('Error in findByName repository:', error);
      throw error;
    }
  }

  /**
   * Create a new Focus
   * @param {Object} data - Focus data
   * @returns {Promise<Focus>} Created Focus
   * @throws {ApiException} If name already exists
   */
  async create(data) {
    try {
      // Check if a focus with this name already exists (case-insensitive)
      const existingFocus = await this.findByName(data.name);

      if (existingFocus) {
        throw new ApiException(409, FOCUS.ALREADY_EXISTS);
      }

      return await Focus.create(data);
    } catch (error) {
      console.error('Error in create repository:', error);
      throw error;
    }
  }

  /**
   * Update Focus
   * @param {string} id - Focus UUID
   * @param {Object} data - Data to update
   * @returns {Promise<Focus>} Updated Focus
   * @throws {ApiException} If Focus not found or name already exists
   */
  async update(id, data) {
    try {
      const focus = await this.findById(id);

      // If name is being updated, check if it already exists
      if (data.name && data.name.toLowerCase() !== focus.name.toLowerCase()) {
        const existingFocus = await this.findByName(data.name);

        if (existingFocus) {
          throw new ApiException(409, FOCUS.ALREADY_EXISTS);
        }
      }

      await focus.update(data);
      return focus;
    } catch (error) {
      console.error('Error in update repository:', error);
      throw error;
    }
  }

  /**
   * Delete Focus
   * @param {string} id - Focus UUID
   * @returns {Promise<boolean>} True if deleted
   * @throws {ApiException} If Focus not found
   */
  async delete(id) {
    try {
      const focus = await this.findById(id);
      await focus.destroy();
      return true;
    } catch (error) {
      console.error('Error in delete repository:', error);
      throw error;
    }
  }
}

// Export singleton instance
module.exports = new FocusRepository();
