'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('Insight', 'status', {
      type: Sequelize.ENUM('PENDING', 'APPROVED', 'REJECTED'),
      allowNull: false,
      defaultValue: 'PENDING',
    });

    await queryInterface.addColumn('Insight', 'reviewedBy', {
      type: Sequelize.UUID,
      allowNull: true,
      references: {
        model: 'Admin',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    await queryInterface.addColumn('Insight', 'reviewedAt', {
      type: Sequelize.DATE,
      allowNull: true,
    });

    await queryInterface.addColumn('Insight', 'rejectionReason', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    // Update existing insights to APPROVED status
    await queryInterface.sequelize.query(`
      UPDATE "Insight" SET status = 'APPROVED'
    `);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('Insight', 'rejectionReason');
    await queryInterface.removeColumn('Insight', 'reviewedAt');
    await queryInterface.removeColumn('Insight', 'reviewedBy');
    await queryInterface.removeColumn('Insight', 'status');

    // Remove the ENUM type
    await queryInterface.sequelize.query(`
      DROP TYPE IF EXISTS "enum_Insight_status";
    `);
  },
};
