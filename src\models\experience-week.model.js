/**
 * ExperienceWeek Model
 * Represents individual weeks within an experience
 */
const { Model, DataTypes } = require('sequelize');

class ExperienceWeek extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        experienceId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'Experience',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        weekNumber: {
          type: DataTypes.INTEGER,
          allowNull: false,
          validate: {
            min: 1,
          },
        },
        title: {
          type: DataTypes.TEXT,
          allowNull: false,
          validate: {
            notEmpty: true,
          },
        },
        weeklyWhy: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
      },
      {
        sequelize,
        modelName: 'ExperienceWeek',
        tableName: 'ExperienceWeek',
        timestamps: true,
        indexes: [
          {
            fields: ['experienceId'],
            name: 'experience_week_experience_id_idx',
          },
          {
            fields: ['weekNumber'],
            name: 'experience_week_number_idx',
          },
          {
            unique: true,
            fields: ['experienceId', 'weekNumber'],
            name: 'experience_week_unique_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // Belongs to Experience
    this.belongsTo(models.Experience, {
      foreignKey: 'experienceId',
      as: 'experience',
      onDelete: 'CASCADE',
    });

    // Has many week media
    this.hasMany(models.ExperienceWeekMedia, {
      foreignKey: 'experienceWeekId',
      as: 'media',
      onDelete: 'CASCADE',
    });

    // Has many experience week insights
    this.hasMany(models.ExperienceWeekInsight, {
      foreignKey: 'experienceWeekId',
      as: 'insights',
      onDelete: 'CASCADE',
    });
  }

  // Instance methods
  toJSON() {
    const values = { ...this.get() };

    // Add computed fields
    if (this.insights && Array.isArray(this.insights)) {
      values.totalInsights = this.insights.length;
    }

    return values;
  }
}

module.exports = ExperienceWeek;
