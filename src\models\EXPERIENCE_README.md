# Experience Models Documentation

## 📋 Overview

The Experience system allows users to create structured learning experiences with weekly breakdowns, insights, and media attachments. This document provides a comprehensive guide to all Experience-related models and their relationships.

## 🏗️ Model Architecture

### Core Models Hierarchy

```
Experience (Main Entity)
├── ExperienceMedia (Experience-level media)
├── ExperienceWeek (Weekly breakdown)
│   ├── ExperienceWeekMedia (Week-specific media)
│   └── ExperienceWeekInsight (Weekly insights, max 5)
│       ├── ExperienceWeekInsightFocus (Insight → Focus categories)
│       ├── ExperienceWeekInsightPdCategory (Insight → PD categories)
│       └── ExperienceWeekInsightWtdCategory (Insight → WTD categories)
├── ExperiencePdCategory (Experience → PD categories)
└── ExperienceWtdCategory (Experience → WTD categories)
```

## 📊 Model Details

### 1. **Experience** (Main Entity)
**File:** `experience.model.js`  
**Table:** `Experience`

| Field | Type | Description |
|-------|------|-------------|
| `id` | UUID | Primary key |
| `title` | TEXT | Experience title |
| `shortDescription` | TEXT | Brief description |
| `longDescription` | TEXT | Detailed description |
| `experienceLength` | INTEGER | Duration in weeks (1-52) |
| `personalNote` | TEXT | Creator's personal notes |
| `createdBy` | UUID | Foreign key to User |

**Relationships:**
- Belongs to User (creator)
- Has many ExperienceMedia
- Has many ExperienceWeek
- Many-to-many with PdCategory (through ExperiencePdCategory)
- Many-to-many with WtdCategory (through ExperienceWtdCategory)

### 2. **ExperienceMedia**
**File:** `experience-media.model.js`  
**Table:** `ExperienceMedia`

| Field | Type | Description |
|-------|------|-------------|
| `id` | UUID | Primary key |
| `experienceId` | UUID | Foreign key to Experience |
| `type` | ENUM | Media type (IMAGE, VIDEO, DOCUMENT, LINK, AUDIO) |
| `url` | TEXT | Media URL |
| `title` | STRING | Media title |
| `order` | INTEGER | Display order |

### 3. **ExperienceWeek**
**File:** `experience-week.model.js`  
**Table:** `ExperienceWeek`

| Field | Type | Description |
|-------|------|-------------|
| `id` | UUID | Primary key |
| `experienceId` | UUID | Foreign key to Experience |
| `weekNumber` | INTEGER | Week number (1, 2, 3...) |
| `title` | TEXT | Week title |
| `weeklyWhy` | TEXT | Week's purpose/rationale |

**Constraints:**
- Unique combination of (experienceId, weekNumber)
- Week numbers must be sequential

### 4. **ExperienceWeekMedia**
**File:** `experience-week-media.model.js`  
**Table:** `ExperienceWeekMedia`

| Field | Type | Description |
|-------|------|-------------|
| `id` | UUID | Primary key |
| `experienceWeekId` | UUID | Foreign key to ExperienceWeek |
| `type` | ENUM | Media type |
| `url` | TEXT | Media URL |
| `title` | STRING | Media title |
| `order` | INTEGER | Display order |

### 5. **ExperienceWeekInsight**
**File:** `experience-week-insight.model.js`  
**Table:** `ExperienceWeekInsight`

| Field | Type | Description |
|-------|------|-------------|
| `id` | UUID | Primary key |
| `experienceWeekId` | UUID | Foreign key to ExperienceWeek |
| `order` | INTEGER | Insight order within week (1-5) |
| `text` | TEXT | Insight content |
| `sourceUrl` | TEXT | Optional source URL |

**Constraints:**
- Maximum 5 insights per week
- Unique combination of (experienceWeekId, order)

## 🔗 Junction Tables

### 6. **ExperiencePdCategory**
**File:** `experience-pd-category.model.js`  
**Table:** `ExperiencePdCategory`

Links Experience with PdCategory (many-to-many)

| Field | Type | Description |
|-------|------|-------------|
| `id` | UUID | Primary key |
| `experienceId` | UUID | Foreign key to Experience |
| `pdCategoryId` | UUID | Foreign key to PdCategory |

### 7. **ExperienceWtdCategory**
**File:** `experience-wtd-category.model.js`  
**Table:** `ExperienceWtdCategory`

Links Experience with WtdCategory (many-to-many)

| Field | Type | Description |
|-------|------|-------------|
| `id` | UUID | Primary key |
| `experienceId` | UUID | Foreign key to Experience |
| `wtdCategoryId` | UUID | Foreign key to WtdCategory |

### 8. **ExperienceWeekInsightFocus**
**File:** `experience-week-insight-focus.model.js`  
**Table:** `ExperienceWeekInsightFocus`

Links ExperienceWeekInsight with Focus (many-to-many)

| Field | Type | Description |
|-------|------|-------------|
| `id` | UUID | Primary key |
| `experienceWeekInsightId` | UUID | Foreign key to ExperienceWeekInsight |
| `focusId` | UUID | Foreign key to Focus |

### 9. **ExperienceWeekInsightPdCategory**
**File:** `experience-week-insight-pd-category.model.js`  
**Table:** `ExperienceWeekInsightPdCategory`

Links ExperienceWeekInsight with PdCategory (many-to-many)

| Field | Type | Description |
|-------|------|-------------|
| `id` | UUID | Primary key |
| `experienceWeekInsightId` | UUID | Foreign key to ExperienceWeekInsight |
| `pdCategoryId` | UUID | Foreign key to PdCategory |

### 10. **ExperienceWeekInsightWtdCategory**
**File:** `experience-week-insight-wtd-category.model.js`  
**Table:** `ExperienceWeekInsightWtdCategory`

Links ExperienceWeekInsight with WtdCategory (many-to-many)

| Field | Type | Description |
|-------|------|-------------|
| `id` | UUID | Primary key |
| `experienceWeekInsightId` | UUID | Foreign key to ExperienceWeekInsight |
| `wtdCategoryId` | UUID | Foreign key to WtdCategory |

## 🎯 Foreign Key Naming Convention

All foreign keys follow the pattern: `{ReferencedModelName}Id`

- `experienceId` → References `Experience.id`
- `experienceWeekId` → References `ExperienceWeek.id`
- `experienceWeekInsightId` → References `ExperienceWeekInsight.id`
- `focusId` → References `Focus.id`
- `pdCategoryId` → References `PdCategory.id`
- `wtdCategoryId` → References `WtdCategory.id`

## 📈 Business Rules

### Experience Rules
- Experience length must be between 1-52 weeks
- Each experience must have a creator (User)
- Experience can have multiple media attachments

### Week Rules
- Week numbers must be sequential (1, 2, 3...)
- Each week must belong to an experience
- Week numbers are unique within an experience

### Insight Rules
- Maximum 5 insights per week
- Insight order must be unique within a week (1-5)
- Insights can be categorized with multiple categories
- Source URL is optional

### Media Rules
- Media can be attached at experience or week level
- Supported types: IMAGE, VIDEO, DOCUMENT, LINK, AUDIO
- Media has ordering for display purposes

## 🗄️ Database Indexes

### Performance Indexes
- `Experience`: createdBy, experienceLength
- `ExperienceWeek`: experienceId, weekNumber
- `ExperienceWeekInsight`: experienceWeekId, order
- `ExperienceMedia`: experienceId, order
- `ExperienceWeekMedia`: experienceWeekId, order

### Unique Constraints
- `ExperienceWeek`: (experienceId, weekNumber)
- `ExperienceWeekInsight`: (experienceWeekId, order)
- All junction tables: composite unique on foreign key pairs

## 🚀 Usage Examples

### Creating an Experience with Weeks
```javascript
// Create experience
const experience = await Experience.create({
  title: "Learning React",
  experienceLength: 4,
  createdBy: userId
});

// Create weeks
for (let i = 1; i <= 4; i++) {
  await ExperienceWeek.create({
    experienceId: experience.id,
    weekNumber: i,
    title: `Week ${i}: React Fundamentals`
  });
}
```

### Adding Insights to a Week
```javascript
const week = await ExperienceWeek.findOne({
  where: { experienceId, weekNumber: 1 }
});

await ExperienceWeekInsight.create({
  experienceWeekId: week.id,
  order: 1,
  text: "Components are the building blocks of React"
});
```

## 📁 File Structure

```
src/models/
├── experience.model.js
├── experience-media.model.js
├── experience-week.model.js
├── experience-week-media.model.js
├── experience-week-insight.model.js
├── experience-pd-category.model.js
├── experience-wtd-category.model.js
├── experience-week-insight-focus.model.js
├── experience-week-insight-pd-category.model.js
├── experience-week-insight-wtd-category.model.js
└── EXPERIENCE_README.md (this file)
```

## 🔧 Migration Files

All migration files are located in `src/migrations/` with timestamps:
- `20250523000005-create-experience.js`
- `20250523000006-create-experience-media.js`
- `20250523000007-create-experience-week.js`
- `20250523000008-create-experience-week-media.js`
- `20250523000009-create-experience-week-insight.js`
- `20250523000010-create-experience-pd-category.js`
- `20250523000011-create-experience-wtd-category.js`
- `20250523000012-create-experience-week-insight-focus.js`
- `20250523000013-create-experience-week-insight-pd-category.js`
- `20250523000014-create-experience-week-insight-wtd-category.js`

## 📞 Support

For questions about the Experience models, refer to:
- Database configuration: `src/config/database.config.js`
- Repository pattern: `src/models/repositories/experience.repository.js`
- Model associations: Individual model files
