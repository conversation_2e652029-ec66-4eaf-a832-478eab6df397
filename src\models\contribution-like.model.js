/**
 * ContributionLike Model
 * Represents user likes/dislikes on contributions
 */
const { Model, DataTypes } = require('sequelize');

class ContributionLike extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
          allowNull: false,
        },
        contributionId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'Contribution',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        likedBy: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
      },
      {
        sequelize,
        modelName: 'ContributionLike',
        tableName: 'ContributionLike',
        timestamps: true,
        indexes: [
          {
            unique: true,
            fields: ['contributionId', 'likedBy'],
            name: 'contribution_like_unique_idx',
          },
          {
            fields: ['contributionId'],
            name: 'contribution_like_contribution_id_idx',
          },
          {
            fields: ['likedBy'],
            name: 'contribution_like_liked_by_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // Belongs to Contribution
    this.belongsTo(models.Contribution, {
      foreignKey: 'contributionId',
      as: 'contribution',
      onDelete: 'CASCADE',
    });

    // Belongs to User
    this.belongsTo(models.User, {
      foreignKey: 'likedBy',
      as: 'user',
      onDelete: 'CASCADE',
    });
  }

  // Static methods
  static async toggleLike(contributionId, userId) {
    const existingLike = await this.findOne({
      where: { contributionId, likedBy: userId },
    });

    if (existingLike) {
      // Like exists - remove it (unlike)
      await existingLike.destroy();
      return { action: 'removed', liked: false };
    } else {
      // No like exists - create it
      await this.create({ contributionId, likedBy: userId });
      return { action: 'created', liked: true };
    }
  }

  static async getUserLikeStatus(contributionId, userId) {
    const like = await this.findOne({
      where: { contributionId, likedBy: userId },
      attributes: ['id'],
    });

    return like ? true : false; // true = liked, false = not liked
  }

  static async getContributionLikeStats(contributionId) {
    const likes = await this.findAll({
      where: { contributionId },
      attributes: ['id'],
    });

    const likesCount = likes.length; // All entries are likes

    return {
      contributionId,
      likesCount,
    };
  }
}

module.exports = ContributionLike;
