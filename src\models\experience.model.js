/**
 * Experience Model
 * Represents educational experiences with weeks and insights
 */
const { Model, DataTypes } = require('sequelize');

class Experience extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        title: {
          type: DataTypes.TEXT,
          allowNull: false,
          validate: {
            notEmpty: true,
          },
        },
        shortDescription: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        longDescription: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        experienceLength: {
          type: DataTypes.INTEGER,
          allowNull: false,
          validate: {
            min: 1,
            max: 52, // Maximum 52 weeks
          },
          comment: 'Length of experience in weeks',
        },
        personalNote: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        createdBy: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
      },
      {
        sequelize,
        modelName: 'Experience',
        tableName: 'Experience',
        timestamps: true,
        indexes: [
          {
            fields: ['createdBy'],
            name: 'experience_created_by_idx',
          },
          {
            fields: ['experienceLength'],
            name: 'experience_length_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // Belongs to User (creator)
    this.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onDelete: 'CASCADE',
    });

    // Has many experience weeks
    this.hasMany(models.ExperienceWeek, {
      foreignKey: 'experienceId',
      as: 'weeks',
      onDelete: 'CASCADE',
    });

    // Has many experience media
    this.hasMany(models.ExperienceMedia, {
      foreignKey: 'experienceId',
      as: 'media',
      onDelete: 'CASCADE',
    });

    // Many-to-many with PdCategory through ExperiencePdCategory
    this.belongsToMany(models.PdCategory, {
      through: models.ExperiencePdCategory,
      as: 'pdCategories',
      foreignKey: 'experienceId',
      otherKey: 'pdCategoryId',
      onDelete: 'CASCADE',
    });

    // Many-to-many with WtdCategory through ExperienceWtdCategory
    this.belongsToMany(models.WtdCategory, {
      through: models.ExperienceWtdCategory,
      as: 'wtdCategories',
      foreignKey: 'experienceId',
      otherKey: 'wtdCategoryId',
      onDelete: 'CASCADE',
    });
  }

  // Instance methods
  toJSON() {
    const values = { ...this.get() };

    // Add computed fields
    if (this.weeks && Array.isArray(this.weeks)) {
      values.totalWeeks = this.weeks.length;
      values.totalInsights = this.weeks.reduce((total, week) => {
        return total + (week.insights ? week.insights.length : 0);
      }, 0);
    }

    return values;
  }

  // Get experience progress for a user
  async getProgressForUser(userId) {
    // This would be implemented when we have user progress tracking
    // For now, return basic structure
    return {
      experienceId: this.id,
      userId: userId,
      completedWeeks: 0,
      totalWeeks: this.experienceLength,
      progressPercentage: 0,
      lastAccessedAt: null,
    };
  }
}

module.exports = Experience;
