{"development": {"use_env_variable": "DATABASE_URL", "dialect": "postgres", "define": {"timestamps": true, "underscored": false}, "dialectOptions": {"ssl": false}}, "test": {"use_env_variable": "DATABASE_URL", "dialect": "postgres", "define": {"timestamps": true, "underscored": false}, "dialectOptions": {"ssl": false}}, "production": {"use_env_variable": "DATABASE_URL", "dialect": "postgres", "define": {"timestamps": true, "underscored": false}, "dialectOptions": {"ssl": false}}, "local": {"use_env_variable": "DATABASE_URL", "dialect": "postgres", "define": {"timestamps": true, "underscored": false}, "dialectOptions": {"ssl": false}}}