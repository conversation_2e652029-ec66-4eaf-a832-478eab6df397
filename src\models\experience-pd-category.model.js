/**
 * ExperiencePdCategory Model
 * Junction table for Experience and PdCategory many-to-many relationship
 */
const { Model, DataTypes } = require('sequelize');

class ExperiencePdCategory extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        experienceId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'Experience',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        pdCategoryId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'PdCategory',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
      },
      {
        sequelize,
        modelName: 'ExperiencePdCategory',
        tableName: 'ExperiencePdCategory',
        timestamps: true,
        indexes: [
          {
            unique: true,
            fields: ['experienceId', 'pdCategoryId'],
            name: 'experience_pd_categories_unique_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // No direct associations needed as this is a junction table
    // Associations are defined in the main models
  }
}

module.exports = ExperiencePdCategory;
