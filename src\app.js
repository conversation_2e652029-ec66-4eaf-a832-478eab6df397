const path = require('path');
const express = require('express');
const compression = require('compression');
const bodyParser = require('body-parser');
const logger = require('morgan');
const lusca = require('lusca');
const cors = require('cors');

// Load environment variables and module aliases
require('dotenv').config();
require('module-alias/register');

// Import custom modules using module aliases
const { determinePlatform } = require('@middlewares/platform.middleware');
const {
  errorHandler,
  notFoundHandler,
} = require('@middlewares/error.middleware');
const Logger = require('@utils/logger.utils');
const databaseService = require('@config/database.config');
const models = require('@models');

/**
 * Create Express server.
 */
const app = express();

/**
 * Express configuration.
 */
const PORT = process.env.PORT || 3001;
app.set('port', PORT);

/**
 * Initialize application
 */
const initializeApp = async () => {
  try {
    // Initialize database first
    await databaseService.init();
    Logger.info('Database initialized successfully');

    // Initialize models
    await models.init();
    Logger.info('Models initialized successfully');

    // Enable CORS
    app.use(cors());

    // GZIP compression
    app.use(compression());

    // Parse request bodies
    app.use(bodyParser.json({ limit: '50mb' }));
    app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));

    // JWT is used for authentication instead of sessions

    // Security headers
    app.use(lusca.xframe('SAMEORIGIN'));
    app.use(lusca.xssProtection(true));
    app.disable('x-powered-by');

    // Logging
    if (process.env.NODE_ENV !== 'test') {
      app.use(logger('dev'));
    }

    // Debug logging in development
    if (process.env.NODE_ENV === 'development') {
      app.use((req, _, next) => {
        Logger.debug('Request', {
          method: req.method,
          url: req.url,
          body: req.body,
          query: req.query,
        });
        next();
      });
    }

    // Platform detection middleware
    app.use(determinePlatform);

    // Serve static files
    app.use(
      '/',
      express.static(path.join(__dirname, '..', 'public'), {
        maxAge: 31557600000,
      })
    );

    // View engine setup
    app.set('views', path.join(__dirname, '..', 'public'));
    app.set('view engine', 'pug');

    // API Documentation (only in non-production)
    if (process.env.NODE_ENV !== 'production') {
      // Legacy API docs (src/api-docs)
      const apiDoc = require('@api');
      app.use('/api-docs', apiDoc);
    }

    // Routes
    const routes = require('./routes');
    app.use('/', routes);

    // Global error handling
    process.on('unhandledRejection', (reason, p) => {
      Logger.error('Unhandled Rejection', { promise: p, reason });
    });

    process.on('uncaughtException', (error) => {
      Logger.error('Uncaught Exception', error);
      // Give the server time to log the error before exiting
      setTimeout(() => {
        process.exit(1);
      }, 1000);
    });

    // 404 handler
    app.use(notFoundHandler);

    // Error handler
    app.use(errorHandler);
  } catch (error) {
    Logger.error('Failed to initialize application:', error);
    process.exit(1);
  }
};

// Handle uncaught errors
process.on('unhandledRejection', (err) => {
  Logger.error('Unhandled Rejection:', err);
  process.exit(1);
});

process.on('uncaughtException', (err) => {
  Logger.error('Uncaught Exception:', err);
  process.exit(1);
});

// Start the application
initializeApp();

module.exports = app;
