const fs = require('fs');
const path = require('path');
const databaseService = require('@config/database.config');

class Models {
  constructor() {
    this.sequelize = null;
    this.models = {};
  }

  async init() {
    try {
      // Get sequelize instance
      this.sequelize = await databaseService.init();

      // Load all models
      const modelFiles = fs
        .readdirSync(__dirname)
        .filter((file) => file.endsWith('.model.js'));

      // Initialize each model
      for (const file of modelFiles) {
        const Model = require(path.join(__dirname, file));
        Model.init(this.sequelize);
        this.models[Model.name] = Model;
      }

      // Initialize associations if they exist
      Object.values(this.models).forEach((model) => {
        if (model.associate) {
          model.associate(this.models);
        }
      });

      return this.models;
    } catch (error) {
      throw new Error(`Error initializing models: ${error.message}`);
    }
  }

  getModel(name) {
    if (!this.models[name]) {
      throw new Error(`Model ${name} not found`);
    }
    return this.models[name];
  }
}

// Export singleton instance
const models = new Models();
module.exports = models;
