/**
 * PD Category Controller
 *
 * Handles PD category-related HTTP requests
 */
const pdCategoryService = require('./pd-category.service');
const { ApiResponse } = require('@utils/response.utils');
const { PD_CATEGORY } = require('@utils/messages.utils');

/**
 * PD Category controller
 */
const pdCategoryController = {
  /**
   * Get all PD categories with pagination and optional search
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getAllCategories: async (req, res, next) => {
    try {
      // Extract query parameters
      const { page = 1, limit = 10, search = '' } = req.query;

      // Get categories with pagination
      const result = await pdCategoryService.getAllCategories({
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        search,
      });

      return ApiResponse.success(
        res,
        PD_CATEGORY.ALL_RETRIEVED,
        result.categories,
        {
          pagination: result.pagination,
        }
      );
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get PD category by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getCategoryById: async (req, res, next) => {
    try {
      const { id } = req.params;
      const category = await pdCategoryService.getCategoryById(id);
      return ApiResponse.success(res, PD_CATEGORY.RETRIEVED, category);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Create a new PD category
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  createCategory: async (req, res, next) => {
    try {
      const { name } = req.body;
      const category = await pdCategoryService.createCategory({ name });
      return ApiResponse.created(res, PD_CATEGORY.CREATED, category);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Update PD category
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  updateCategory: async (req, res, next) => {
    try {
      const { id } = req.params;
      const { name } = req.body;
      const category = await pdCategoryService.updateCategory(id, { name });
      return ApiResponse.success(res, PD_CATEGORY.UPDATED, category);
    } catch (error) {
      next(error);
    }
  },

  /**
   * Delete PD category
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  deleteCategory: async (req, res, next) => {
    try {
      const { id } = req.params;
      await pdCategoryService.deleteCategory(id);
      return ApiResponse.success(res, PD_CATEGORY.DELETED);
    } catch (error) {
      next(error);
    }
  },
};

module.exports = pdCategoryController;
