/**
 * PD Category Repository
 *
 * Handles data access operations for the PD Category model
 */
const { ApiException } = require('@utils/exception.utils');
const PdCategory = require('@models/pd-category.model');
const { PD_CATEGORY } = require('@utils/messages.utils');
const { Op } = require('sequelize');

class PdCategoryRepository {
  /**
   * Find all PD categories with pagination and optional search
   * @param {Object} options - Query options
   * @param {number} options.page - Page number (1-based)
   * @param {number} options.limit - Number of items per page
   * @param {string} options.search - Optional search term for name
   * @returns {Promise<Object>} Object containing categories and pagination info
   */
  async findAll({ page = 1, limit = 10, search = '' } = {}) {
    try {
      // Build where clause for search
      const whereClause = {};
      if (search) {
        whereClause.name = {
          [Op.iLike]: `%${search}%`,
        };
      }

      // Calculate offset
      const offset = (page - 1) * limit;

      // Get total count
      const count = await PdCategory.count({ where: whereClause });

      // Get paginated results
      const categories = await PdCategory.findAll({
        where: whereClause,
        order: [['name', 'ASC']],
        limit,
        offset,
      });

      // Calculate pagination metadata
      const totalPages = Math.ceil(count / limit);
      const hasNext = page < totalPages;
      const hasPrevious = page > 1;

      return {
        categories,
        pagination: {
          total: count,
          page,
          limit,
          totalPages,
          hasNext,
          hasPrevious,
        },
      };
    } catch (error) {
      console.error('Error in findAll repository:', error);
      throw error;
    }
  }

  /**
   * Find PD category by ID
   * @param {string} id - PD category UUID
   * @returns {Promise<PdCategory>} PD category instance
   * @throws {ApiException} If PD category not found
   */
  async findById(id) {
    try {
      const pdCategory = await PdCategory.findByPk(id);

      if (!pdCategory) {
        throw new ApiException(404, PD_CATEGORY.NOT_FOUND);
      }

      return pdCategory;
    } catch (error) {
      console.error('Error in findById repository:', error);
      throw error;
    }
  }

  /**
   * Find PD category by name (case-insensitive)
   * @param {string} name - PD category name
   * @returns {Promise<PdCategory|null>} PD category instance or null if not found
   */
  async findByName(name) {
    try {
      return await PdCategory.findOne({
        where: {
          name: {
            [Op.iLike]: name,
          },
        },
      });
    } catch (error) {
      console.error('Error in findByName repository:', error);
      throw error;
    }
  }

  /**
   * Create a new PD category
   * @param {Object} data - PD category data
   * @returns {Promise<PdCategory>} Created PD category
   * @throws {ApiException} If name already exists
   */
  async create(data) {
    try {
      // Check if a category with this name already exists (case-insensitive)
      const existingCategory = await this.findByName(data.name);

      if (existingCategory) {
        throw new ApiException(409, PD_CATEGORY.ALREADY_EXISTS);
      }

      return await PdCategory.create(data);
    } catch (error) {
      console.error('Error in create repository:', error);
      throw error;
    }
  }

  /**
   * Update PD category
   * @param {string} id - PD category UUID
   * @param {Object} data - Data to update
   * @returns {Promise<PdCategory>} Updated PD category
   * @throws {ApiException} If PD category not found or name already exists
   */
  async update(id, data) {
    try {
      const pdCategory = await this.findById(id);

      // If name is being updated, check if it already exists
      if (
        data.name &&
        data.name.toLowerCase() !== pdCategory.name.toLowerCase()
      ) {
        const existingCategory = await this.findByName(data.name);

        if (existingCategory) {
          throw new ApiException(409, PD_CATEGORY.ALREADY_EXISTS);
        }
      }

      await pdCategory.update(data);
      return pdCategory;
    } catch (error) {
      console.error('Error in update repository:', error);
      throw error;
    }
  }

  /**
   * Delete PD category
   * @param {string} id - PD category UUID
   * @returns {Promise<boolean>} True if deleted
   * @throws {ApiException} If PD category not found
   */
  async delete(id) {
    try {
      const pdCategory = await this.findById(id);
      await pdCategory.destroy();
      return true;
    } catch (error) {
      console.error('Error in delete repository:', error);
      throw error;
    }
  }
}

// Export singleton instance
module.exports = new PdCategoryRepository();
