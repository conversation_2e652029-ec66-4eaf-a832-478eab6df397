/**
 * User PD Category Module
 *
 * This module handles PD category-related functionality for regular users
 */
const express = require('express');
const router = express.Router();
const { validate } = require('@middlewares/validation.middleware');
const { authenticate } = require('@middlewares/auth.middleware');
const userPdCategoryController = require('./pd-category.controller');
const pdCategoryValidation = require('@admin/pd-category/pd-category.validation');

/**
 * Register routes
 */
function registerRoutes() {
  // Get all PD categories
  router.get(
    '/',
    authenticate,
    validate(pdCategoryValidation.getAll),
    userPdCategoryController.getAllCategories
  );

  // Get a PD category by ID
  router.get(
    '/:id',
    authenticate,
    validate(pdCategoryValidation.getById),
    userPdCategoryController.getCategoryById
  );

  return router;
}

// Export the router
module.exports = registerRoutes();
