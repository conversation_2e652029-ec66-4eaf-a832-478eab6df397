/**
 * API Exception
 *
 * Custom error class for API errors
 */

/**
 * API Exception class
 * @extends Error
 */
class ApiException extends Error {
  /**
   * Create an API exception
   * @param {number} statusCode - HTTP status code
   * @param {string} message - Error message
   * @param {*} errors - Additional error details
   */
  constructor(statusCode, message, errors = null) {
    super(message);
    this.name = 'ApiException';
    this.statusCode = statusCode;
    this.errors = errors;
  }
}

module.exports = {
  ApiException,
};
