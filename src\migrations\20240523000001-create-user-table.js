'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('User', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      firstName: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      lastName: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      email: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true,
      },
      password: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      userType: {
        type: Sequelize.ENUM('EDUCATOR', 'EDUCATOR_PLUS', 'PROVIDER_PLUS'),
        allowNull: false,
        defaultValue: 'EDUCATOR',
      },
      position: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      focus: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      state: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      country: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      companyName: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });

    // Add a hook to convert email to lowercase
    await queryInterface.sequelize.query(`
      CREATE OR REPLACE FUNCTION convert_email_to_lowercase()
      RETURNS trigger AS $$
      BEGIN
        NEW.email = LOWER(NEW.email);
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      DROP TRIGGER IF EXISTS user_email_lowercase ON "User";
      
      CREATE TRIGGER user_email_lowercase
      BEFORE INSERT OR UPDATE ON "User"
      FOR EACH ROW
      EXECUTE FUNCTION convert_email_to_lowercase();
    `);
  },

  async down(queryInterface, Sequelize) {
    // Drop the trigger and function first
    await queryInterface.sequelize.query(`
      DROP TRIGGER IF EXISTS user_email_lowercase ON "User";
      DROP FUNCTION IF EXISTS convert_email_to_lowercase();
    `);

    // Drop the table
    await queryInterface.dropTable('User');
  },
};
