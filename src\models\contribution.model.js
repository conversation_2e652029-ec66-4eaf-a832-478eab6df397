/**
 * Contribution Model
 * Represents user contributions (comments) on insights
 */
const { Model, DataTypes } = require('sequelize');

class Contribution extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
          allowNull: false,
        },
        insightId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'Insight',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        contributedBy: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        content: {
          type: DataTypes.TEXT,
          allowNull: false,
        },
      },
      {
        sequelize,
        modelName: 'Contribution',
        tableName: 'Contribution',
        timestamps: true,
        indexes: [
          {
            fields: ['insightId'],
            name: 'contribution_insight_id_idx',
          },
          {
            fields: ['contributedBy'],
            name: 'contribution_contributed_by_idx',
          },
          {
            fields: ['createdAt'],
            name: 'contribution_created_at_idx',
          },
          {
            fields: ['insightId', 'createdAt'],
            name: 'contribution_insight_created_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // Belongs to Insight
    this.belongsTo(models.Insight, {
      foreignKey: 'insightId',
      as: 'insight',
      onDelete: 'CASCADE',
    });

    // Belongs to User (contributor)
    this.belongsTo(models.User, {
      foreignKey: 'contributedBy',
      as: 'contributor',
      onDelete: 'CASCADE',
    });

    // Has many contribution likes
    this.hasMany(models.ContributionLike, {
      foreignKey: 'contributionId',
      as: 'likes',
      onDelete: 'CASCADE',
    });
  }

  // Instance methods
  toJSON() {
    const values = { ...this.get() };

    // Add computed fields
    if (this.likes && Array.isArray(this.likes)) {
      const likesCount = this.likes.length; // All entries are likes now

      values.likesCount = likesCount;
    }

    return values;
  }

  // Static methods
  static async getContributionStats(contributionId) {
    const contribution = await this.findByPk(contributionId, {
      include: [
        {
          model: this.sequelize.models.ContributionLike,
          as: 'likes',
          attributes: ['id'], // Just need count, no isLike field
        },
      ],
    });

    if (!contribution) return null;

    const likes = contribution.likes || [];
    const likesCount = likes.length; // All entries are likes

    return {
      contributionId,
      likesCount,
    };
  }
}

module.exports = Contribution;
