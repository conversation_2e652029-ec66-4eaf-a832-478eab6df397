/**
 * Utility helper functions
 */

/**
 * Generate a random OTP
 * @param {number} length - Length of OTP
 * @returns {number} - Generated OTP
 */
const genOtp = (length = 4) => {
  const min = Math.pow(10, length - 1);
  const max = Math.pow(10, length) - 1;
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

/**
 * Generate a standardized API response
 * @param {number} code - HTTP status code
 * @param {string} message - Response message
 * @param {Object|Array} data - Response data
 * @param {Object} meta - Additional metadata
 * @returns {Object} - Formatted response object
 */
const genRes = (code, message, data = null, meta = null) => {
  const response = {
    status: code >= 200 && code < 300 ? 'success' : 'error',
    message,
  };

  if (data !== null) {
    response.data = data;
  }

  if (meta !== null) {
    response.meta = meta;
  }

  return response;
};

/**
 * Generate a CloudFront URL for a given key
 * @param {string} key - S3 object key
 * @returns {string} - CloudFront URL
 */
const generateCloudFrontUrl = (key) => {
  if (!key) return null;

  const cloudFrontDomain = process.env.CLOUDFRONT_DOMAIN || '';
  return `https://${cloudFrontDomain}/${key}`;
};

/**
 * Get the uploads path for a given type
 * @param {string} type - Upload type
 * @returns {string} - Upload path
 */
const getUploadsPath = (type) => {
  const basePath = 'uploads';

  switch (type) {
    case 'profile':
      return `${basePath}/profiles`;
    case 'document':
      return `${basePath}/documents`;
    case 'attachment':
      return `${basePath}/attachments`;
    default:
      return basePath;
  }
};

module.exports = {
  genOtp,
  genRes,
  generateCloudFrontUrl,
  getUploadsPath,
};
