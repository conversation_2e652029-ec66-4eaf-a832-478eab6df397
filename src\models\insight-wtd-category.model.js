/**
 * InsightWtdCategory Model
 * Represents the many-to-many relationship between Insight and WtdCategory
 */
const { Model, DataTypes } = require('sequelize');

class InsightWtdCategory extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        insightId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'Insight',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        wtdCategoryId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'WtdCategory',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
      },
      {
        sequelize,
        modelName: 'InsightWtdCategory',
        tableName: 'InsightWtdCategory',
        timestamps: true,
        indexes: [
          {
            unique: true,
            fields: ['insightId', 'wtdCategoryId'],
            name: 'insight_wtd_category_unique_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // No direct associations needed as this is a junction table
  }
}

module.exports = InsightWtdCategory;
