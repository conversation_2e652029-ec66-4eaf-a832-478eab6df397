/**
 * Migration: Create ExperienceWeek table
 */
'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('ExperienceWeek', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      experienceId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'Experience',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      weekNumber: {
        type: Sequelize.INTEGER,
        allowNull: false,
        validate: {
          min: 1,
        },
      },
      title: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      weeklyWhy: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });

    // Add indexes
    await queryInterface.addIndex('ExperienceWeek', ['experienceId'], {
      name: 'experience_week_experience_id_idx',
    });

    await queryInterface.addIndex('ExperienceWeek', ['weekNumber'], {
      name: 'experience_week_number_idx',
    });

    // Add unique constraint for experienceId + weekNumber
    await queryInterface.addIndex(
      'ExperienceWeek',
      ['experienceId', 'weekNumber'],
      {
        unique: true,
        name: 'experience_week_unique_idx',
      }
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('ExperienceWeek');
  },
};
