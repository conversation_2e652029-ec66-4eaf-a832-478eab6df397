/**
 * ContributionReport Model
 * Represents reports made against contributions
 */
const { Model, DataTypes } = require('sequelize');

class ContributionReport extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
          allowNull: false,
        },
        contributionId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'Contribution',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        reportedBy: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        reason: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
      },
      {
        sequelize,
        modelName: 'ContributionReport',
        tableName: 'ContributionReport',
        timestamps: true,
        indexes: [
          {
            fields: ['contributionId'],
          },
          {
            fields: ['reportedBy'],
          },
          {
            fields: ['createdAt'],
          },
          {
            unique: true,
            fields: ['contributionId', 'reportedBy'],
            name: 'unique_contribution_report_per_user',
          },
        ],
      }
    );
  }

  static associate(models) {
    // Belongs to Contribution
    this.belongsTo(models.Contribution, {
      foreignKey: 'contributionId',
      as: 'contribution',
      onDelete: 'CASCADE',
    });

    // Belongs to User (reporter)
    this.belongsTo(models.User, {
      foreignKey: 'reportedBy',
      as: 'reporter',
      onDelete: 'CASCADE',
    });
  }

  // Instance methods
  toJSON() {
    const values = { ...this.get() };
    return values;
  }
}

module.exports = ContributionReport;
