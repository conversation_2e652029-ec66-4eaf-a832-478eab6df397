/**
 * ExperienceWeekInsightPdCategory Model
 * Junction table for ExperienceWeekInsight and PdCategory many-to-many relationship
 */
const { Model, DataTypes } = require('sequelize');

class ExperienceWeekInsightPdCategory extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        experienceWeekInsightId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'ExperienceWeekInsight',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        pdCategoryId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'PdCategory',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
      },
      {
        sequelize,
        modelName: 'ExperienceWeekInsightPdCategory',
        tableName: 'ExperienceWeekInsightPdCategory',
        timestamps: true,
        indexes: [
          {
            unique: true,
            fields: ['experienceWeekInsightId', 'pdCategoryId'],
            name: 'experience_week_insight_pd_category_unique_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // No direct associations needed as this is a junction table
    // Associations are defined in the main models
  }
}

module.exports = ExperienceWeekInsightPdCategory;
