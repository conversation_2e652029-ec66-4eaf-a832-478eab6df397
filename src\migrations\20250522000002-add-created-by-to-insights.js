'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('Insight', 'createdBy', {
      type: Sequelize.UUID,
      allowNull: true, // Initially allow null for existing records
      references: {
        model: 'User',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });

    // Set a default admin user as the creator for existing insights
    // This is just a placeholder - in a real system, you might want to
    // set this to a specific admin user or handle it differently
    const [users] = await queryInterface.sequelize.query(
      `SELECT id FROM "User" LIMIT 1`
    );

    if (users.length > 0) {
      const defaultUserId = users[0].id;
      await queryInterface.sequelize.query(
        `UPDATE "Insight" SET "createdBy" = '${defaultUserId}' WHERE "createdBy" IS NULL`
      );
    }

    // Now make the column not nullable
    await queryInterface.changeColumn('Insight', 'createdBy', {
      type: Sequelize.UUID,
      allowNull: false,
      references: {
        model: 'User',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('Insight', 'createdBy');
  },
};
