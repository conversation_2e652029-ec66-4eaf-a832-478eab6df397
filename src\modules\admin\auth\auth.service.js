/**
 * Admin Auth Service
 *
 * Handles admin authentication-related business logic
 */
const jwt = require('jsonwebtoken');
const { ApiException } = require('@utils/exception.utils');
const adminRepository = require('@models/repositories/admin.repository');
const { AUTH, ADMIN } = require('@utils/messages.utils');
const { UserRole } = require('@utils/enums.utils');

/**
 * Admin auth service
 */
const authService = {
  /**
   * Generate JWT token
   * @param {Object} payload - Data to be included in token
   * @returns {string} JWT token
   */
  generateToken: (payload) => {
    return jwt.sign(payload, process.env.JWT_SECRET);
  },

  /**
   * Verify JWT token
   * @param {string} token - JWT token to verify
   * @returns {Object} Decoded token payload
   */
  verifyToken: (token) => {
    return jwt.verify(token, process.env.JWT_SECRET);
  },

  /**
   * Login admin
   * @param {string} email - Admin email
   * @param {string} password - Admin password
   * @returns {Object} Admin data and token
   */
  loginAdmin: async (email, password) => {
    try {
      // Find admin
      const admin = await adminRepository.findByEmail(email);

      if (!admin) {
        throw new ApiException(401, AUTH.EMAIL_NOT_FOUND);
      }

      // Check password
      const isPasswordValid = await admin.verifyPassword(password);

      if (!isPasswordValid) {
        throw new ApiException(401, AUTH.INVALID_CREDENTIALS);
      }

      // Generate token (without expiration)
      const token = jwt.sign(
        { id: admin.id, email: admin.email, role: UserRole.ADMIN },
        process.env.JWT_SECRET
      );

      // Return admin data with token
      return {
        id: admin.id,
        email: admin.email,
        role: UserRole.ADMIN,
        token: token,
      };
    } catch (error) {
      // Log the error for debugging
      console.error('Error in loginAdmin service:', error);
      throw error;
    }
  },

  /**
   * Get admin profile
   * @param {string} adminId - Admin ID
   * @returns {Object} Admin profile data
   */
  getAdminProfile: async (adminId) => {
    try {
      // Find admin
      const admin = await adminRepository.findById(adminId);

      if (!admin) {
        throw new ApiException(404, ADMIN.ADMIN_NOT_FOUND);
      }

      // Return admin data (without password)
      return {
        id: admin.id,
        email: admin.email,
        role: UserRole.ADMIN,
      };
    } catch (error) {
      // Log the error for debugging
      console.error('Error in getAdminProfile service:', error);
      throw error;
    }
  },
};

module.exports = authService;
