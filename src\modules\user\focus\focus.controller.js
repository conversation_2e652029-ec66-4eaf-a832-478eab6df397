/**
 * User Focus Controller
 *
 * Handles Focus-related HTTP requests for regular users
 */
const focusService = require('@admin/focus/focus.service');
const { ApiResponse } = require('@utils/response.utils');
const { FOCUS } = require('@utils/messages.utils');

/**
 * User Focus controller
 */
const userFocusController = {
  /**
   * Get all focus areas with pagination and optional search
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getAllFocusAreas: async (req, res, next) => {
    try {
      // Extract query parameters
      const { page = 1, limit = 10, search = '' } = req.query;

      // Get focus areas with pagination
      const result = await focusService.getAllFocusAreas({
        page: parseInt(page, 10),
        limit: parseInt(limit, 10),
        search,
      });

      return ApiResponse.success(res, FOCUS.ALL_RETRIEVED, result.focus, {
        pagination: result.pagination,
      });
    } catch (error) {
      next(error);
    }
  },

  /**
   * Get focus by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   * @param {Function} next - Express next function
   */
  getFocusById: async (req, res, next) => {
    try {
      const { id } = req.params;
      const focus = await focusService.getFocusById(id);
      return ApiResponse.success(res, FOCUS.RETRIEVED, focus);
    } catch (error) {
      next(error);
    }
  },
};

module.exports = userFocusController;
