/**
 * PD Category Module
 *
 * This module handles PD category-related functionality
 */
const express = require('express');
const router = express.Router();
const { validate } = require('@middlewares/validation.middleware');
const { authenticate } = require('@middlewares/auth.middleware');
const pdCategoryController = require('./pd-category.controller');
const pdCategoryValidation = require('./pd-category.validation');

/**
 * Register routes
 */
function registerRoutes() {
  // Get all PD categories
  router.get(
    '/',
    authenticate,
    validate(pdCategoryValidation.getAll),
    pdCategoryController.getAllCategories
  );

  // Get a PD category by ID
  router.get(
    '/:id',
    authenticate,
    validate(pdCategoryValidation.getById),
    pdCategoryController.getCategoryById
  );

  // Create a new PD category
  router.post(
    '/',
    authenticate,
    validate(pdCategoryValidation.create),
    pdCategoryController.createCategory
  );

  // Update a PD category
  router.put(
    '/:id',
    authenticate,
    validate(pdCategoryValidation.update),
    pdCategoryController.updateCategory
  );

  // Delete a PD category
  router.delete(
    '/:id',
    authenticate,
    validate(pdCategoryValidation.delete),
    pdCategoryController.deleteCategory
  );

  return router;
}

// Export the router
module.exports = registerRoutes();
