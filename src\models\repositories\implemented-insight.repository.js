/**
 * ImplementedInsight Repository
 *
 * Handles data access operations for the ImplementedInsight model
 */
const { ApiException } = require('@utils/exception.utils');
const databaseService = require('@config/database.config');
const { INSIGHT } = require('@utils/messages.utils');

class ImplementedInsightRepository {
  /**
   * Toggle implement status for an insight
   * @param {string} userId - User UUID
   * @param {string} insightId - Insight UUID
   * @returns {Promise<Object>} Object with isImplemented status
   * @throws {ApiException} If insight not found
   */
  async toggleImplement(userId, insightId) {
    try {
      // Get models
      const ImplementedInsight = databaseService.getImplementedInsightModel();
      const Insight = databaseService.getInsightModel();

      // Check if insight exists
      const insight = await Insight.findByPk(insightId);
      if (!insight) {
        throw new ApiException(404, INSIGHT.NOT_FOUND);
      }

      // Check if already implemented
      const existingImplement = await ImplementedInsight.findOne({
        where: {
          userId,
          insightId,
        },
      });

      // If already implemented, remove it
      if (existingImplement) {
        await existingImplement.destroy();
        return { isImplemented: false };
      }

      // Otherwise, create a new implement
      await ImplementedInsight.create({
        userId,
        insightId,
      });

      return { isImplemented: true };
    } catch (error) {
      console.error('Error in toggleImplement repository:', error);
      throw error;
    }
  }

  /**
   * Check if an insight is implemented by a user
   * @param {string} userId - User UUID
   * @param {string} insightId - Insight UUID
   * @returns {Promise<boolean>} True if implemented
   */
  async isImplemented(userId, insightId) {
    try {
      const ImplementedInsight = databaseService.getImplementedInsightModel();

      const implement = await ImplementedInsight.findOne({
        where: {
          userId,
          insightId,
        },
      });

      return !!implement;
    } catch (error) {
      console.error('Error in isImplemented repository:', error);
      throw error;
    }
  }
}

module.exports = new ImplementedInsightRepository();
