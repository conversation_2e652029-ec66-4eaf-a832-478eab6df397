/**
 * User Auth Validation Schemas
 *
 * Defines validation schemas for user authentication-related requests
 */
const { body } = require('express-validator');
const { VALIDATION } = require('@utils/messages.utils');

/**
 * User auth validation schemas
 */
const authValidation = {
  /**
   * Register validation schema
   */
  register: [
    body('firstName').notEmpty().withMessage(VALIDATION.FIRST_NAME_REQUIRED),
    body('lastName').notEmpty().withMessage(VALIDATION.LAST_NAME_REQUIRED),
    body('email').isEmail().withMessage(VALIDATION.EMAIL_INVALID),
    body('password')
      .isLength({ min: 6 })
      .withMessage(VALIDATION.PASSWORD_LENGTH),
  ],

  /**
   * Login validation schema
   */
  login: [
    body('email').isEmail().withMessage(VALIDATION.EMAIL_INVALID),
    body('password').notEmpty().withMessage(VALIDATION.REQUIRED),
  ],

  /**
   * Update profile validation schema
   */
  updateProfile: [
    body('firstName')
      .optional()
      .notEmpty()
      .withMessage(VALIDATION.FIRST_NAME_REQUIRED),
    body('lastName')
      .optional()
      .notEmpty()
      .withMessage(VALIDATION.LAST_NAME_REQUIRED),
    body('position').optional(),
    body('focus').optional(),
    body('state').optional(),
    body('country').optional(),
    body('companyName').optional(),
    body('profilePic').optional().isURL().withMessage(VALIDATION.INVALID_URL),
  ],
};

module.exports = authValidation;
