'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('Insight', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      insightText: {
        type: Sequelize.STRING(250),
        allowNull: false,
      },
      sourceUrl: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      pdCategoryId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'PdCategory',
          key: 'id',
        },
        onUpdate: 'NO ACTION',
        onDelete: 'CASCADE',
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('Insight');
  },
};
