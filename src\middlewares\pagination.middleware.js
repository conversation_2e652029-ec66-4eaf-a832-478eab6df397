/**
 * Pagination Middleware
 *
 * Middleware to handle pagination parameters in request queries
 * Converts page and limit to integers with default values
 */
const paginationMiddleware = (req, res, next) => {
  const { page = 1, limit = 10 } = req.query;

  // Convert to integers and set defaults
  req.pagination = {
    page: parseInt(page, 10) || 1,
    limit: parseInt(limit, 10) || 10,
  };

  next();
};

module.exports = paginationMiddleware;
