/**
 * Common Repository
 *
 * Provides common functionality and utilities that can be used across all repositories.
 * This includes pagination, data formatting, and other shared operations.
 */
class CommonRepository {
  /**
   * Build pagination information object
   * @param {number} count - Total number of items
   * @param {number} page - Current page number (1-based)
   * @param {number} limit - Number of items per page
   * @returns {Object} Pagination information object
   */
  buildPaginationInfo(count, page, limit) {
    const totalPages = Math.ceil(count / limit);
    return {
      total: count,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrevious: page > 1,
    };
  }

  /**
   * Calculate offset for pagination
   * @param {number} page - Current page number (1-based)
   * @param {number} limit - Number of items per page
   * @returns {number} Offset value
   */
  calculateOffset(page, limit) {
    return (page - 1) * limit;
  }

  // Future common methods can be added here, such as:
  // - Data formatting utilities
  // - Common validation methods
  // - Shared query builders
  // - Common error handling
  // - Shared data transformation methods
  // - Common filtering logic
  // - etc.
}

module.exports = new CommonRepository();
