/**
 * ExperienceWeekInsightWtdCategory Model
 * Junction table for ExperienceWeekInsight and WtdCategory many-to-many relationship
 */
const { Model, DataTypes } = require('sequelize');

class ExperienceWeekInsightWtdCategory extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        experienceWeekInsightId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'ExperienceWeekInsight',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        wtdCategoryId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'WtdCategory',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
      },
      {
        sequelize,
        modelName: 'ExperienceWeekInsightWtdCategory',
        tableName: 'ExperienceWeekInsightWtdCategory',
        timestamps: true,
        indexes: [
          {
            unique: true,
            fields: ['experienceWeekInsightId', 'wtdCategoryId'],
            name: 'experience_week_insight_wtd_category_unique_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // No direct associations needed as this is a junction table
    // Associations are defined in the main models
  }
}

module.exports = ExperienceWeekInsightWtdCategory;
