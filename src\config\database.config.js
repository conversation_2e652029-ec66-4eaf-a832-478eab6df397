/**
 * Database Configuration and Connection Management
 */
const { Sequelize } = require('sequelize');
const Logger = require('@utils/logger.utils');
const { DB, IS_LOCAL } = require('@config/app.config');

// Import all models
const Admin = require('@models/admin.model');
const User = require('@models/user.model');
const Insight = require('@models/insight.model');
const Focus = require('@models/focus.model');
const PdCategory = require('@models/pd-category.model');
const WtdCategory = require('@models/wtd-category.model');
const InsightFocus = require('@models/insight-focus.model');
const InsightWtdCategory = require('@models/insight-wtd-category.model');
const BookmarkedInsight = require('@models/bookmarked-insight.model');
const LikedInsight = require('@models/liked-insight.model');
const ImplementedInsight = require('@models/implemented-insight.model');
const Membership = require('@models/membership.model');

// Import experience-related models
const Experience = require('@models/experience.model');
const ExperienceMedia = require('@models/experience-media.model');
const ExperienceWeek = require('@models/experience-week.model');
const ExperienceWeekMedia = require('@models/experience-week-media.model');
const ExperienceWeekInsight = require('@models/experience-week-insight.model');
const ExperienceWeekInsightWtdCategory = require('@models/experience-week-insight-wtd-category.model');
const ExperienceWeekInsightPdCategory = require('@models/experience-week-insight-pd-category.model');
const ExperienceWeekInsightFocus = require('@models/experience-week-insight-focus.model');
const ExperiencePdCategory = require('@models/experience-pd-category.model');
const ExperienceWtdCategory = require('@models/experience-wtd-category.model');

// Contribution models
const Contribution = require('@models/contribution.model');
const ContributionLike = require('@models/contribution-like.model');

// Import report models
const InsightReport = require('@models/insight-report.model');
const ContributionReport = require('@models/contribution-report.model');

// Database instance
let sequelize;

/**
 * Database service
 */
const databaseService = {
  /**
   * Initialize database connection
   * @param {Object} app - Express application
   * @returns {Object} Sequelize instance
   */
  init: async (app) => {
    try {
      // Get database configuration
      const env = process.env.NODE_ENV || 'development';
      Logger.info(`Initializing database connection for environment: ${env}`);

      // Configure SSL based on environment
      const sslConfig = IS_LOCAL
        ? false // Disable SSL for localhost
        : {
            // Enable SSL for development and production
            require: true,
            rejectUnauthorized: false, // Allow self-signed certificates
          };

      Logger.info(
        `SSL ${IS_LOCAL ? 'disabled' : 'enabled'} for database connection`
      );

      // Create Sequelize instance using configuration
      if (process.env.DATABASE_URL) {
        Logger.info('Connecting to database using DATABASE_URL');
        sequelize = new Sequelize(process.env.DATABASE_URL, {
          dialect: 'postgres',
          logging: DB.logging,
          define: DB.define,
          pool: DB.pool,
          dialectOptions: {
            ssl: sslConfig,
          },
        });
      } else {
        Logger.info(
          'Connecting to database using individual configuration parameters'
        );
        sequelize = new Sequelize(DB.database, DB.username, DB.password, {
          host: DB.host,
          port: DB.port,
          dialect: DB.dialect,
          logging: DB.logging,
          define: DB.define,
          pool: DB.pool,
          dialectOptions: {
            ssl: sslConfig,
          },
        });
      }

      // Test connection
      await sequelize.authenticate();
      Logger.success('Database connection established successfully');

      // Attach sequelize to app
      if (app) {
        app.set('sequelize', sequelize);
        Logger.info('Sequelize instance attached to app');
      }

      // Initialize models
      Admin.init(sequelize);
      User.init(sequelize);
      Membership.init(sequelize);

      // Initialize junction table models
      InsightFocus.init(sequelize);
      InsightWtdCategory.init(sequelize);
      BookmarkedInsight.init(sequelize);
      LikedInsight.init(sequelize);
      ImplementedInsight.init(sequelize);
      ExperienceWeekInsightWtdCategory.init(sequelize);
      ExperienceWeekInsightPdCategory.init(sequelize);
      ExperienceWeekInsightFocus.init(sequelize);
      ExperiencePdCategory.init(sequelize);
      ExperienceWtdCategory.init(sequelize);

      // Initialize contribution models
      Contribution.init(sequelize);
      ContributionLike.init(sequelize);

      // Initialize report models
      InsightReport.init(sequelize);
      ContributionReport.init(sequelize);

      // Initialize other models
      Insight.init(sequelize);
      Focus.init(sequelize);
      PdCategory.init(sequelize);
      WtdCategory.init(sequelize);

      // Initialize experience models
      Experience.init(sequelize);
      ExperienceMedia.init(sequelize);
      ExperienceWeek.init(sequelize);
      ExperienceWeekMedia.init(sequelize);
      ExperienceWeekInsight.init(sequelize);

      // Create a models object for associations
      const models = {
        Admin,
        User,
        Insight,
        Focus,
        PdCategory,
        WtdCategory,
        InsightFocus,
        InsightWtdCategory,
        BookmarkedInsight,
        LikedInsight,
        ImplementedInsight,
        Membership,
        Experience,
        ExperienceMedia,
        ExperienceWeek,
        ExperienceWeekMedia,
        ExperienceWeekInsight,
        ExperienceWeekInsightWtdCategory,
        ExperienceWeekInsightPdCategory,
        ExperienceWeekInsightFocus,
        ExperiencePdCategory,
        ExperienceWtdCategory,
        Contribution,
        ContributionLike,
        InsightReport,
        ContributionReport,
      };

      // Set up associations
      Object.values(models).forEach((model) => {
        if (model.associate) {
          model.associate(models);
        }
      });

      return sequelize;
    } catch (error) {
      Logger.error('Unable to connect to the database', error);
      Logger.warn(
        'Continuing without database connection. Some features may not work.'
      );
    }
  },

  /**
   * Get Sequelize instance
   * @returns {Object} Sequelize instance
   */
  getSequelize: () => {
    if (!sequelize) {
      throw new Error('Database not initialized');
    }
    return sequelize;
  },

  /**
   * Close database connection
   */
  close: async () => {
    if (sequelize) {
      await sequelize.close();
      Logger.info('Database connection closed');
    }
  },

  /**
   * Get database configuration for the current environment
   * @returns {Object} Database configuration
   */
  getConfig: () => {
    return DB;
  },

  /**
   * Get Admin model
   * @returns {Object} Admin model
   */
  getAdminModel: () => {
    return Admin;
  },

  /**
   * Get User model
   * @returns {Object} User model
   */
  getUserModel: () => {
    return User;
  },

  /**
   * Get Insight model
   * @returns {Object} Insight model
   */
  getInsightModel: () => {
    return Insight;
  },

  /**
   * Get Focus model
   * @returns {Object} Focus model
   */
  getFocusModel: () => {
    return Focus;
  },

  /**
   * Get PdCategory model
   * @returns {Object} PdCategory model
   */
  getPdCategoryModel: () => {
    return PdCategory;
  },

  /**
   * Get WtdCategory model
   * @returns {Object} WtdCategory model
   */
  getWtdCategoryModel: () => {
    return WtdCategory;
  },

  /**
   * Get InsightFocus model
   * @returns {Object} InsightFocus model
   */
  getInsightFocusModel: () => {
    return InsightFocus;
  },

  /**
   * Get InsightWtdCategory model
   * @returns {Object} InsightWtdCategory model
   */
  getInsightWtdCategoryModel: () => {
    return InsightWtdCategory;
  },

  /**
   * Get BookmarkedInsight model
   * @returns {Object} BookmarkedInsight model
   */
  getBookmarkedInsightModel: () => {
    return BookmarkedInsight;
  },

  /**
   * Get LikedInsight model
   * @returns {Object} LikedInsight model
   */
  getLikedInsightModel: () => {
    return LikedInsight;
  },

  /**
   * Get ImplementedInsight model
   * @returns {Object} ImplementedInsight model
   */
  getImplementedInsightModel: () => {
    return ImplementedInsight;
  },

  /**
   * Get Membership model
   * @returns {Object} Membership model
   */
  getMembershipModel: () => {
    return Membership;
  },

  /**
   * Get Contribution model
   * @returns {Object} Contribution model
   */
  getContributionModel: () => {
    return Contribution;
  },

  /**
   * Get ContributionLike model
   * @returns {Object} ContributionLike model
   */
  getContributionLikeModel: () => {
    return ContributionLike;
  },

  /**
   * Get InsightReport model
   * @returns {Object} InsightReport model
   */
  getInsightReportModel: () => {
    return InsightReport;
  },

  /**
   * Get ContributionReport model
   * @returns {Object} ContributionReport model
   */
  getContributionReportModel: () => {
    return ContributionReport;
  },

  /**
   * Get Experience model
   * @returns {Object} Experience model
   */
  getExperienceModel: () => {
    return Experience;
  },

  /**
   * Get ExperienceMedia model
   * @returns {Object} ExperienceMedia model
   */
  getExperienceMediaModel: () => {
    return ExperienceMedia;
  },

  /**
   * Get ExperienceWeek model
   * @returns {Object} ExperienceWeek model
   */
  getExperienceWeekModel: () => {
    return ExperienceWeek;
  },

  /**
   * Get ExperienceWeekMedia model
   * @returns {Object} ExperienceWeekMedia model
   */
  getExperienceWeekMediaModel: () => {
    return ExperienceWeekMedia;
  },

  /**
   * Get ExperienceWeekInsight model
   * @returns {Object} ExperienceWeekInsight model
   */
  getExperienceWeekInsightModel: () => {
    return ExperienceWeekInsight;
  },

  /**
   * Get ExperienceWeekInsightWtdCategory model
   * @returns {Object} ExperienceWeekInsightWtdCategory model
   */
  getExperienceWeekInsightWtdCategoryModel: () => {
    return ExperienceWeekInsightWtdCategory;
  },

  /**
   * Get ExperienceWeekInsightPdCategory model
   * @returns {Object} ExperienceWeekInsightPdCategory model
   */
  getExperienceWeekInsightPdCategoryModel: () => {
    return ExperienceWeekInsightPdCategory;
  },

  /**
   * Get ExperienceWeekInsightFocus model
   * @returns {Object} ExperienceWeekInsightFocus model
   */
  getExperienceWeekInsightFocusModel: () => {
    return ExperienceWeekInsightFocus;
  },

  /**
   * Get ExperiencePdCategory model
   * @returns {Object} ExperiencePdCategory model
   */
  getExperiencePdCategoryModel: () => {
    return ExperiencePdCategory;
  },

  /**
   * Get ExperienceWtdCategory model
   * @returns {Object} ExperienceWtdCategory model
   */
  getExperienceWtdCategoryModel: () => {
    return ExperienceWtdCategory;
  },
};

module.exports = databaseService;
