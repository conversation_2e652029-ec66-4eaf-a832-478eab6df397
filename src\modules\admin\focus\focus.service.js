/**
 * Focus Service
 *
 * Handles Focus-related business logic
 */
const focusRepository = require('@models/repositories/focus.repository');

/**
 * Focus service
 */
const focusService = {
  /**
   * Get all focus areas with pagination and optional search
   * @param {Object} options - Query options
   * @param {number} options.page - Page number (1-based)
   * @param {number} options.limit - Number of items per page
   * @param {string} options.search - Optional search term for name
   * @returns {Promise<Object>} Object containing focus areas and pagination info
   */
  getAllFocusAreas: async ({ page, limit, search } = {}) => {
    try {
      return await focusRepository.findAll({ page, limit, search });
    } catch (error) {
      console.error('Error in getAllFocusAreas service:', error);
      throw error;
    }
  },

  /**
   * Get focus by ID
   * @param {string} id - Focus ID
   * @returns {Promise<Object>} Focus data
   */
  getFocusById: async (id) => {
    try {
      return await focusRepository.findById(id);
    } catch (error) {
      console.error('Error in getFocusById service:', error);
      throw error;
    }
  },

  /**
   * Create a new focus
   * @param {Object} data - Focus data
   * @returns {Promise<Object>} Created focus
   */
  createFocus: async (data) => {
    try {
      return await focusRepository.create(data);
    } catch (error) {
      console.error('Error in createFocus service:', error);
      throw error;
    }
  },

  /**
   * Update focus
   * @param {string} id - Focus ID
   * @param {Object} data - Data to update
   * @returns {Promise<Object>} Updated focus
   */
  updateFocus: async (id, data) => {
    try {
      return await focusRepository.update(id, data);
    } catch (error) {
      console.error('Error in updateFocus service:', error);
      throw error;
    }
  },

  /**
   * Delete focus
   * @param {string} id - Focus ID
   * @returns {Promise<boolean>} True if deleted
   */
  deleteFocus: async (id) => {
    try {
      return await focusRepository.delete(id);
    } catch (error) {
      console.error('Error in deleteFocus service:', error);
      throw error;
    }
  },
};

module.exports = focusService;
