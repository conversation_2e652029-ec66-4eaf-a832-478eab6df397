/**
 * User Auth Service
 *
 * Contains business logic for user authentication
 */
const jwt = require('jsonwebtoken');
const { ApiException } = require('@utils/exception.utils');
const userRepository = require('@models/repositories/user.repository');
const { AUTH } = require('@utils/messages.utils');
const { UserRole } = require('@utils/enums.utils');

/**
 * User auth service
 */
const authService = {
  /**
   * Generate JWT token
   * @param {Object} payload - Data to be included in token
   * @returns {string} JWT token
   */
  generateToken: (payload) => {
    return jwt.sign(payload, process.env.JWT_SECRET);
  },

  /**
   * Verify JWT token
   * @param {string} token - JWT token to verify
   * @returns {Object} Decoded token payload
   */
  verifyToken: (token) => {
    return jwt.verify(token, process.env.JWT_SECRET);
  },

  /**
   * Register a new user
   * @param {Object} userData - User registration data
   * @returns {Object} User data and token
   */
  registerUser: async (userData) => {
    const { firstName, lastName, email, password } = userData;

    // Check if user already exists
    const existingUser = await userRepository.findByEmail(email);
    if (existingUser) {
      throw new ApiException(409, AUTH.EMAIL_ALREADY_EXISTS);
    }

    // Create user
    const user = await userRepository.create({
      firstName,
      lastName,
      email,
      password, // Will be hashed by model hooks
    });

    // Generate token
    const token = jwt.sign(
      {
        id: user.id,
        email: user.email,
        userType: user.userType,
        role: UserRole.USER,
      },
      process.env.JWT_SECRET
    );

    // Return user data (without password)
    return {
      user: {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        userType: user.userType,
        role: UserRole.USER,
      },
      token,
    };
  },

  /**
   * Login user
   * @param {string} email - User email
   * @param {string} password - User password
   * @returns {Object} User data and token
   */
  loginUser: async (email, password) => {
    try {
      // Find user
      const user = await userRepository.findByEmail(email);

      if (!user) {
        throw new ApiException(401, AUTH.EMAIL_NOT_FOUND);
      }

      // Verify password
      const isPasswordValid = await user.verifyPassword(password);

      if (!isPasswordValid) {
        throw new ApiException(401, AUTH.INVALID_CREDENTIALS);
      }

      // Generate token (without expiration)
      const token = jwt.sign(
        {
          id: user.id,
          email: user.email,
          userType: user.userType,
          role: UserRole.USER,
        },
        process.env.JWT_SECRET
      );

      // Return user data with token
      return {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        userType: user.userType,
        position: user.position,
        focus: user.focus,
        state: user.state,
        country: user.country,
        companyName: user.companyName,
        profilePic: user.profilePic,
        role: UserRole.USER,
        token: token,
      };
    } catch (error) {
      // Log the error for debugging
      console.error('Error in loginUser service:', error);
      throw error;
    }
  },

  /**
   * Get user profile
   * @param {string} userId - User ID
   * @returns {Object} User profile data
   */
  getUserProfile: async (userId) => {
    try {
      const user = await userRepository.findById(userId);

      if (!user) {
        throw new ApiException(404, AUTH.USER_NOT_FOUND);
      }

      return {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        userType: user.userType,
        position: user.position,
        focus: user.focus,
        state: user.state,
        country: user.country,
        companyName: user.companyName,
        profilePic: user.profilePic,
        role: UserRole.USER,
      };
    } catch (error) {
      console.error('Error in getUserProfile service:', error);
      throw error;
    }
  },

  /**
   * Update user profile
   * @param {string} userId - User ID
   * @param {Object} profileData - Updated profile data
   * @returns {Object} Updated user profile data
   */
  updateUserProfile: async (userId, profileData) => {
    try {
      // Find user
      const user = await userRepository.findById(userId);

      if (!user) {
        throw new ApiException(404, AUTH.USER_NOT_FOUND);
      }

      // Update user data
      const allowedFields = [
        'firstName',
        'lastName',
        'position',
        'focus',
        'state',
        'country',
        'companyName',
        'profilePic',
      ];

      const updateData = {};
      allowedFields.forEach((field) => {
        if (profileData[field] !== undefined) {
          updateData[field] = profileData[field];
        }
      });

      // Update user in database
      const updatedUser = await userRepository.update(userId, updateData);

      // Return updated user data
      return {
        id: updatedUser.id,
        firstName: updatedUser.firstName,
        lastName: updatedUser.lastName,
        email: updatedUser.email,
        userType: updatedUser.userType,
        position: updatedUser.position,
        focus: updatedUser.focus,
        state: updatedUser.state,
        country: updatedUser.country,
        companyName: updatedUser.companyName,
        profilePic: updatedUser.profilePic,
        role: UserRole.USER,
      };
    } catch (error) {
      console.error('Error in updateUserProfile service:', error);
      throw error;
    }
  },
};

module.exports = authService;
