/**
 * Contribution Repository
 * Handles database operations for contributions and contribution likes
 */
const Contribution = require('@models/contribution.model');
const ContributionLike = require('@models/contribution-like.model');
const User = require('@models/user.model');
const Insight = require('@models/insight.model');

class ContributionRepository {
  /**
   * Find contribution by ID
   * @param {string} id - Contribution ID
   * @param {boolean} includeStats - Include like/dislike stats
   * @returns {Promise<Object|null>} Contribution or null
   */
  async findById(id, includeStats = true) {
    try {
      const includeOptions = [
        {
          model: User,
          as: 'contributor',
          attributes: ['id', 'firstName', 'lastName', 'email'],
        },
        {
          model: Insight,
          as: 'insight',
          attributes: ['id', 'insightText'],
        },
      ];

      if (includeStats) {
        includeOptions.push({
          model: ContributionLike,
          as: 'likes',
          attributes: ['id'],
          required: false,
        });
      }

      return await Contribution.findByPk(id, {
        include: includeOptions,
      });
    } catch (error) {
      throw new Error(`Error finding contribution: ${error.message}`);
    }
  }

  /**
   * Create a new contribution
   * @param {Object} contributionData - Contribution data
   * @returns {Promise<Object>} Created contribution
   */
  async create(contributionData) {
    try {
      const contribution = await Contribution.create(contributionData);
      return await this.findById(contribution.id);
    } catch (error) {
      throw new Error(`Error creating contribution: ${error.message}`);
    }
  }

  /**
   * Toggle like on a contribution
   * @param {string} contributionId - Contribution ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} Like action result
   */
  async toggleLike(contributionId, userId) {
    try {
      return await ContributionLike.toggleLike(contributionId, userId);
    } catch (error) {
      throw new Error(`Error toggling contribution like: ${error.message}`);
    }
  }

  /**
   * Get contributions by insight with pagination
   * @param {string} insightId - Insight ID
   * @param {Object} options - Query options
   * @param {number} options.page - Page number (default: 1)
   * @param {number} options.limit - Items per page (default: 10)
   * @param {string} options.orderBy - Order field (default: 'createdAt')
   * @param {string} options.orderDirection - Order direction (default: 'DESC')
   * @returns {Promise<Object>} Object with contributions and pagination
   */
  async findByInsight(insightId, options = {}) {
    try {
      const {
        page = 1,
        limit = 10,
        orderBy = 'createdAt',
        orderDirection = 'DESC',
      } = options;

      const offset = (page - 1) * limit;
      const whereClause = { insightId };

      const includeOptions = [
        {
          model: User,
          as: 'contributor',
          attributes: ['id', 'firstName', 'lastName', 'email', 'profilePic'],
        },
        {
          model: ContributionLike,
          as: 'likes',
          attributes: ['id', 'likedBy'],
          required: false,
        },
      ];

      const { count, rows } = await Contribution.findAndCountAll({
        where: whereClause,
        include: includeOptions,
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [[orderBy, orderDirection.toUpperCase()]],
        distinct: true,
      });

      return {
        contributions: rows,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: parseInt(limit),
        },
      };
    } catch (error) {
      throw new Error(
        `Error finding contributions by insight: ${error.message}`
      );
    }
  }

  /**
   * Delete a contribution
   * @param {string} id - Contribution ID
   * @returns {Promise<boolean>} True if deleted
   */
  async delete(id) {
    try {
      const contribution = await this.findById(id, false);
      if (!contribution) {
        throw new Error('Contribution not found');
      }

      await contribution.destroy();
      return true;
    } catch (error) {
      throw new Error(`Error deleting contribution: ${error.message}`);
    }
  }
}

module.exports = new ContributionRepository();
