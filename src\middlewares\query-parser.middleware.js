/**
 * Query Parser Middleware
 *
 * Middleware to parse query parameters, handling arrays and other special types
 */

/**
 * Parse query parameters that should be arrays
 * @param {Array<string>} arrayParams - List of query parameter names that should be treated as arrays
 * @returns {Function} Express middleware function
 */
const parseArrayParams = (arrayParams = []) => {
  return (req, res, next) => {
    // Process each parameter that should be an array
    arrayParams.forEach((param) => {
      if (req.query[param]) {
        // If it's already an array, leave it as is
        if (Array.isArray(req.query[param])) {
          // Express already handles repeated query params as arrays
          // e.g., ?focusIds=id1&focusIds=id2 becomes { focusIds: ['id1', 'id2'] }
        }
        // If it's a string, check if it's JSON
        else if (typeof req.query[param] === 'string') {
          try {
            // Try to parse as JSON array
            const parsed = JSON.parse(req.query[param]);
            if (Array.isArray(parsed)) {
              req.query[param] = parsed;
            } else {
              // If it's not an array after parsing, make it a single-item array
              req.query[param] = [req.query[param]];
            }
          } catch (e) {
            // If it's not valid JSON, treat it as a single-item array
            req.query[param] = [req.query[param]];
          }
        }
      } else {
        // If the parameter doesn't exist, initialize it as an empty array
        req.query[param] = [];
      }
    });

    next();
  };
};

module.exports = {
  parseArrayParams,
};
