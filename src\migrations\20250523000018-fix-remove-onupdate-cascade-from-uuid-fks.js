/**
 * Migration: Fix - Remove unnecessary onUpdate CASCADE from UUID foreign keys
 * Fixed SQL query ambiguity issue from previous migration
 */
'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    console.log(
      '🔧 [FIXED] Removing unnecessary onUpdate CASCADE from UUID foreign keys...'
    );

    // Define all foreign key constraints that need updating
    const foreignKeys = [
      // Core tables
      { table: 'Insight', column: 'createdBy', referencedTable: 'User' },
      { table: 'Experience', column: 'createdBy', referencedTable: 'User' },

      // Junction tables - User/Insight relationships
      { table: 'BookmarkedInsight', column: 'userId', referencedTable: 'User' },
      {
        table: 'BookmarkedInsight',
        column: 'insightId',
        referencedTable: 'Insight',
      },
      { table: 'LikedInsight', column: 'userId', referencedTable: 'User' },
      {
        table: 'LikedInsight',
        column: 'insightId',
        referencedTable: 'Insight',
      },
      {
        table: 'ImplementedInsight',
        column: 'userId',
        referencedTable: 'User',
      },
      {
        table: 'ImplementedInsight',
        column: 'insightId',
        referencedTable: 'Insight',
      },

      // Insight category junction tables
      {
        table: 'InsightFocus',
        column: 'insightId',
        referencedTable: 'Insight',
      },
      { table: 'InsightFocus', column: 'focusId', referencedTable: 'Focus' },
      {
        table: 'InsightWtdCategory',
        column: 'insightId',
        referencedTable: 'Insight',
      },
      {
        table: 'InsightWtdCategory',
        column: 'wtdCategoryId',
        referencedTable: 'WtdCategory',
      },

      // Experience tables
      {
        table: 'ExperienceMedia',
        column: 'experienceId',
        referencedTable: 'Experience',
      },
      {
        table: 'ExperienceWeek',
        column: 'experienceId',
        referencedTable: 'Experience',
      },
      {
        table: 'ExperienceWeekMedia',
        column: 'experienceWeekId',
        referencedTable: 'ExperienceWeek',
      },
      {
        table: 'ExperienceWeekInsight',
        column: 'experienceWeekId',
        referencedTable: 'ExperienceWeek',
      },

      // Experience junction tables
      {
        table: 'ExperiencePdCategory',
        column: 'experienceId',
        referencedTable: 'Experience',
      },
      {
        table: 'ExperiencePdCategory',
        column: 'pdCategoryId',
        referencedTable: 'PdCategory',
      },
      {
        table: 'ExperienceWtdCategory',
        column: 'experienceId',
        referencedTable: 'Experience',
      },
      {
        table: 'ExperienceWtdCategory',
        column: 'wtdCategoryId',
        referencedTable: 'WtdCategory',
      },

      // Experience week insight junction tables
      {
        table: 'ExperienceWeekInsightFocus',
        column: 'experienceWeekInsightId',
        referencedTable: 'ExperienceWeekInsight',
      },
      {
        table: 'ExperienceWeekInsightFocus',
        column: 'focusId',
        referencedTable: 'Focus',
      },
      {
        table: 'ExperienceWeekInsightPdCategory',
        column: 'experienceWeekInsightId',
        referencedTable: 'ExperienceWeekInsight',
      },
      {
        table: 'ExperienceWeekInsightPdCategory',
        column: 'pdCategoryId',
        referencedTable: 'PdCategory',
      },
      {
        table: 'ExperienceWeekInsightWtdCategory',
        column: 'experienceWeekInsightId',
        referencedTable: 'ExperienceWeekInsight',
      },
      {
        table: 'ExperienceWeekInsightWtdCategory',
        column: 'wtdCategoryId',
        referencedTable: 'WtdCategory',
      },

      // Contribution tables
      {
        table: 'Contribution',
        column: 'insightId',
        referencedTable: 'Insight',
      },
      {
        table: 'Contribution',
        column: 'contributedBy',
        referencedTable: 'User',
      },
      {
        table: 'ContributionLike',
        column: 'contributionId',
        referencedTable: 'Contribution',
      },
      { table: 'ContributionLike', column: 'likedBy', referencedTable: 'User' },

      // Membership table
      { table: 'Membership', column: 'userId', referencedTable: 'User' },
    ];

    let successCount = 0;
    let skipCount = 0;
    let errorCount = 0;

    for (const fk of foreignKeys) {
      try {
        // Check if table exists
        const tableExists = await queryInterface.sequelize.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_name = '${fk.table}'
          );
        `);

        if (!tableExists[0][0].exists) {
          console.log(`⚠️  Table ${fk.table} does not exist, skipping...`);
          skipCount++;
          continue;
        }

        // Find the constraint name (FIXED: specify tc.constraint_name to avoid ambiguity)
        const [constraints] = await queryInterface.sequelize.query(`
          SELECT tc.constraint_name
          FROM information_schema.table_constraints tc
          JOIN information_schema.key_column_usage kcu
            ON tc.constraint_name = kcu.constraint_name
          WHERE tc.table_name = '${fk.table}'
            AND kcu.column_name = '${fk.column}'
            AND tc.constraint_type = 'FOREIGN KEY'
        `);

        if (constraints.length > 0) {
          const constraintName = constraints[0].constraint_name;

          // Check if constraint has onUpdate CASCADE
          const [constraintDetails] = await queryInterface.sequelize.query(`
            SELECT update_rule
            FROM information_schema.referential_constraints
            WHERE constraint_name = '${constraintName}'
          `);

          if (
            constraintDetails.length > 0 &&
            constraintDetails[0].update_rule === 'CASCADE'
          ) {
            // Drop the existing constraint
            await queryInterface.sequelize.query(`
              ALTER TABLE "${fk.table}" DROP CONSTRAINT "${constraintName}"
            `);

            // Recreate without onUpdate CASCADE
            await queryInterface.sequelize.query(`
              ALTER TABLE "${fk.table}"
              ADD CONSTRAINT "${constraintName}"
              FOREIGN KEY ("${fk.column}")
              REFERENCES "${fk.referencedTable}"(id)
              ON DELETE CASCADE
            `);

            console.log(
              `✅ Updated ${fk.table}.${fk.column} -> ${fk.referencedTable}`
            );
            successCount++;
          } else {
            console.log(
              `ℹ️  ${fk.table}.${fk.column} already clean (no onUpdate CASCADE)`
            );
            skipCount++;
          }
        } else {
          console.log(`⚠️  No constraint found for ${fk.table}.${fk.column}`);
          skipCount++;
        }
      } catch (error) {
        console.error(
          `❌ Failed to update ${fk.table}.${fk.column}:`,
          error.message
        );
        errorCount++;
      }
    }

    console.log(`\n🎉 Migration completed:`);
    console.log(`   ✅ Updated: ${successCount} constraints`);
    console.log(`   ℹ️  Already clean/Skipped: ${skipCount} constraints`);
    console.log(`   ❌ Errors: ${errorCount} constraints`);
    console.log(`   🚀 onUpdate CASCADE removed from UUID foreign keys`);
    console.log(`   📈 Database performance improved!`);
  },

  async down(queryInterface, Sequelize) {
    console.log('⚠️  Rollback not implemented for onUpdate CASCADE removal');
    console.log('   This change improves performance and is safe to keep');
  },
};
