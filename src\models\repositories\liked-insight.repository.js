/**
 * LikedInsight Repository
 *
 * Handles data access operations for the LikedInsight model
 */
const { ApiException } = require('@utils/exception.utils');
const databaseService = require('@config/database.config');
const { INSIGHT } = require('@utils/messages.utils');

class LikedInsightRepository {
  /**
   * Toggle like status for an insight
   * @param {string} userId - User UUID
   * @param {string} insightId - Insight UUID
   * @returns {Promise<Object>} Object with isLiked status
   * @throws {ApiException} If insight not found
   */
  async toggleLike(userId, insightId) {
    try {
      // Get models
      const LikedInsight = databaseService.getLikedInsightModel();
      const Insight = databaseService.getInsightModel();

      // Check if insight exists
      const insight = await Insight.findByPk(insightId);
      if (!insight) {
        throw new ApiException(404, INSIGHT.NOT_FOUND);
      }

      // Check if already liked
      const existingLike = await LikedInsight.findOne({
        where: {
          userId,
          insightId,
        },
      });

      // If already liked, remove it
      if (existingLike) {
        await existingLike.destroy();
        return { isLiked: false };
      }

      // Otherwise, create a new like
      await LikedInsight.create({
        userId,
        insightId,
      });

      return { isLiked: true };
    } catch (error) {
      console.error('Error in toggleLike repository:', error);
      throw error;
    }
  }

  /**
   * Check if an insight is liked by a user
   * @param {string} userId - User UUID
   * @param {string} insightId - Insight UUID
   * @returns {Promise<boolean>} True if liked
   */
  async isLiked(userId, insightId) {
    try {
      const LikedInsight = databaseService.getLikedInsightModel();

      const like = await LikedInsight.findOne({
        where: {
          userId,
          insightId,
        },
      });

      return !!like;
    } catch (error) {
      console.error('Error in isLiked repository:', error);
      throw error;
    }
  }
}

module.exports = new LikedInsightRepository();
