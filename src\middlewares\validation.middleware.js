/**
 * Validation Middleware
 */
const { validationResult } = require('express-validator');
const { ApiException } = require('@utils/exception.utils');

/**
 * Validate middleware
 * Validates request against provided schema
 * @param {Array} validations - Array of validation chains
 */
const validate = (validations) => {
  return async (req, res, next) => {
    try {
      // Run all validations
      await Promise.all(validations.map((validation) => validation.run(req)));

      // Get validation errors
      const errors = validationResult(req);

      if (!errors.isEmpty()) {
        throw new ApiException(400, 'Validation failed', errors.array());
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

module.exports = {
  validate,
};
