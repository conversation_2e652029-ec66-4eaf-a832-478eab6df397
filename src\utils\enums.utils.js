/**
 * Application Enums
 *
 * Contains all application-wide enumerations
 */

/**
 * User roles
 */
const UserRole = {
  ADMIN: 'ADMIN',
  USER: 'USER',
  PROVIDER_PLUS: 'PROVIDER_PLUS',
  values: ['ADMIN', 'USER', 'PROVIDER_PLUS'],
  isValid: (role) => UserRole.values.includes(role),
};

/**
 * User status
 */
const UserStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  PENDING: 'PENDING',
  BLOCKED: 'BLOCKED',
  values: ['ACTIVE', 'INACTIVE', 'PENDING', 'BLOCKED'],
  isValid: (status) => UserStatus.values.includes(status),
};

/**
 * User types
 */
const UserType = {
  EDUCATOR: 'EDUCATOR',
  EDUCATOR_PLUS: 'EDUCATOR_PLUS',
  PROVIDER_PLUS: 'PROVIDER_PLUS',
  values: ['EDUCATOR', 'EDUCATOR_PLUS', 'PROVIDER_PLUS'],
  isValid: (type) => UserType.values.includes(type),
};

/**
 * Membership types
 */
const MembershipType = {
  EDUCATOR_PLUS: 'EDUCATOR_PLUS',
  PROVIDER_PLUS: 'PROVIDER_PLUS',
  values: ['EDUCATOR_PLUS', 'PROVIDER_PLUS'],
  isValid: (type) => MembershipType.values.includes(type),
};

/**
 * Platform types
 */
const PlatformType = {
  WEB: 'WEB',
  MOBILE: 'MOBILE',
  API: 'API',
  values: ['WEB', 'MOBILE', 'API'],
  isValid: (platform) => PlatformType.values.includes(platform),
};

/**
 * HTTP status codes
 */
const HttpStatus = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  INTERNAL_SERVER_ERROR: 500,
};

/**
 * Insight status
 */
const InsightStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  values: ['PENDING', 'APPROVED', 'REJECTED'],
  isValid: (status) => InsightStatus.values.includes(status),
};

/**
 * Media types for experiences and weeks
 */
const MediaType = {
  IMAGE: 'IMAGE',
  VIDEO: 'VIDEO',
  DOCUMENT: 'DOCUMENT',
  LINK: 'LINK',
  AUDIO: 'AUDIO',
  values: ['IMAGE', 'VIDEO', 'DOCUMENT', 'LINK', 'AUDIO'],
  isValid: (type) => MediaType.values.includes(type),
};

/**
 * Report status
 */
const ReportStatus = {
  PENDING: 'PENDING',
  REVIEWED: 'REVIEWED',
  RESOLVED: 'RESOLVED',
  DISMISSED: 'DISMISSED',
  values: ['PENDING', 'REVIEWED', 'RESOLVED', 'DISMISSED'],
  isValid: (status) => ReportStatus.values.includes(status),
};

/**
 * Report reason types
 */
const ReportReason = {
  SPAM: 'SPAM',
  INAPPROPRIATE_CONTENT: 'INAPPROPRIATE_CONTENT',
  HARASSMENT: 'HARASSMENT',
  MISINFORMATION: 'MISINFORMATION',
  COPYRIGHT_VIOLATION: 'COPYRIGHT_VIOLATION',
  HATE_SPEECH: 'HATE_SPEECH',
  OTHER: 'OTHER',
  values: [
    'SPAM',
    'INAPPROPRIATE_CONTENT',
    'HARASSMENT',
    'MISINFORMATION',
    'COPYRIGHT_VIOLATION',
    'HATE_SPEECH',
    'OTHER',
  ],
  isValid: (reason) => ReportReason.values.includes(reason),
};

/**
 * Rejection reasons
 */
const RejectionReason = {
  INAPPROPRIATE_CONTENT: 'INAPPROPRIATE_CONTENT',
  DUPLICATE_CONTENT: 'DUPLICATE_CONTENT',
  LOW_QUALITY: 'LOW_QUALITY',
  OFF_TOPIC: 'OFF_TOPIC',
  OTHER: 'OTHER',
};

module.exports = {
  UserRole,
  UserStatus,
  UserType,
  MembershipType,
  PlatformType,
  HttpStatus,
  InsightStatus,
  MediaType,
  ReportStatus,
  ReportReason,
  RejectionReason,
};
