/**
 * Migration: Create ExperienceWeekInsightPdCategory junction table
 */
'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('ExperienceWeekInsightPdCategory', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      experienceWeekInsightId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'ExperienceWeekInsight',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      pdCategoryId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'PdCategory',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });

    // Add unique constraint for experienceWeekInsightId + pdCategoryId
    await queryInterface.addIndex(
      'ExperienceWeekInsightPdCategory',
      ['experienceWeekInsightId', 'pdCategoryId'],
      {
        unique: true,
        name: 'experience_week_insight_pd_category_unique_idx',
      }
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('ExperienceWeekInsightPdCategory');
  },
};
