/**
 * Migration: Create ExperienceWeekInsight table
 */
'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('ExperienceWeekInsight', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      experienceWeekId: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'ExperienceWeek',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      order: {
        type: Sequelize.INTEGER,
        allowNull: false,
        validate: {
          min: 1,
          max: 5,
        },
        comment: 'Order of insight within week (1-5)',
      },
      text: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      sourceUrl: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });

    // Add indexes
    await queryInterface.addIndex(
      'ExperienceWeekInsight',
      ['experienceWeekId'],
      {
        name: 'experience_week_insight_week_id_idx',
      }
    );

    await queryInterface.addIndex('ExperienceWeekInsight', ['order'], {
      name: 'experience_week_insight_order_idx',
    });

    // Add unique constraint for experienceWeekId + order
    await queryInterface.addIndex(
      'ExperienceWeekInsight',
      ['experienceWeekId', 'order'],
      {
        unique: true,
        name: 'experience_week_insight_week_order_unique_idx',
      }
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('ExperienceWeekInsight');
  },
};
