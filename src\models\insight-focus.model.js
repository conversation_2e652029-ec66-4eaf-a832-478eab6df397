/**
 * InsightFocus Model
 * Represents the many-to-many relationship between Insight and Focus
 */
const { Model, DataTypes } = require('sequelize');

class InsightFocus extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        insightId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'Insight',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        focusId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'Focus',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
      },
      {
        sequelize,
        modelName: 'InsightFocus',
        tableName: 'InsightFocus',
        timestamps: true,
        indexes: [
          {
            unique: true,
            fields: ['insightId', 'focusId'],
            name: 'insight_focus_unique_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // No direct associations needed as this is a junction table
  }
}

module.exports = InsightFocus;
