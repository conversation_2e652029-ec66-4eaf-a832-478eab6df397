/**
 * Migration: Create Experience table
 */
'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('Experience', {
      id: {
        type: Sequelize.UUID,
        defaultValue: Sequelize.UUIDV4,
        primaryKey: true,
        allowNull: false,
      },
      title: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      shortDescription: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      longDescription: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      experienceLength: {
        type: Sequelize.INTEGER,
        allowNull: false,
        validate: {
          min: 1,
          max: 52,
        },
        comment: 'Length of experience in weeks',
      },
      personalNote: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      createdBy: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'User',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
    });

    // Add indexes
    await queryInterface.addIndex('Experience', ['createdBy'], {
      name: 'experience_created_by_idx',
    });

    await queryInterface.addIndex('Experience', ['experienceLength'], {
      name: 'experience_length_idx',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('Experience');
  },
};
