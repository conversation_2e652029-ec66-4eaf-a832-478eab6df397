/**
 * WTD Category Validation
 *
 * Validation schemas for WTD category operations
 */
const { body, param, query } = require('express-validator');

/**
 * WTD Category validation schemas
 */
const wtdCategoryValidation = {
  /**
   * Create WTD category validation schema
   */
  create: [body('name').notEmpty().withMessage('Category name is required')],

  /**
   * Update WTD category validation schema
   */
  update: [
    param('id').isUUID().withMessage('Invalid category ID format'),
    body('name').notEmpty().withMessage('Category name is required'),
  ],

  /**
   * Delete WTD category validation schema
   */
  delete: [param('id').isUUID().withMessage('Invalid category ID format')],

  /**
   * Get WTD category by ID validation schema
   */
  getById: [param('id').isUUID().withMessage('Invalid category ID format')],

  /**
   * Get all WTD categories validation schema
   */
  getAll: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer')
      .toInt(),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
      .toInt(),
    query('search')
      .optional()
      .isString()
      .withMessage('Search must be a string'),
  ],
};

module.exports = wtdCategoryValidation;
