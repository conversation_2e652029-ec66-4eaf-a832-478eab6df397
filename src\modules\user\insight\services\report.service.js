/**
 * Report Service
 * Handles business logic for reporting insights and contributions
 */
const insightRepository = require('@models/repositories/insight.repository');
const contributionRepository = require('@models/repositories/contribution.repository');
const insightReportRepository = require('@models/repositories/insight-report.repository');
const contributionReportRepository = require('@models/repositories/contribution-report.repository');
const { ApiException } = require('@utils/exception.utils');
const { REPORT } = require('@utils/messages.utils');

/**
 * Report service
 */
const reportService = {
  /**
   * Report an insight
   * @param {string} insightId - The insight ID
   * @param {string} userId - The user ID
   * @param {string} reason - The report reason
   * @returns {Object} Created report
   */
  async reportInsight(insightId, userId, reason) {
    try {
      // Check if insight exists
      const insight = await insightRepository.findById(insightId);
      if (!insight) {
        throw new ApiException(404, REPORT.INSIGHT_NOT_FOUND);
      }

      // Check if user is trying to report their own insight
      if (insight.createdBy === userId) {
        throw new ApiException(400, REPORT.CANNOT_REPORT_OWN_CONTENT);
      }

      // Check if user has already reported this insight
      const hasReported = await insightReportRepository.hasUserReported(
        insightId,
        userId
      );
      if (hasReported) {
        throw new ApiException(400, REPORT.ALREADY_REPORTED);
      }

      // Create the report
      const report = await insightReportRepository.create({
        insightId,
        reportedBy: userId,
        reason,
      });

      return report;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Report a contribution
   * @param {string} contributionId - The contribution ID
   * @param {string} userId - The user ID
   * @param {string} reason - The report reason
   * @returns {Object} Created report
   */
  async reportContribution(contributionId, userId, reason) {
    try {
      // Check if contribution exists
      const contribution =
        await contributionRepository.findById(contributionId);
      if (!contribution) {
        throw new ApiException(404, REPORT.CONTRIBUTION_NOT_FOUND);
      }

      // Check if user is trying to report their own contribution
      if (contribution.contributedBy === userId) {
        throw new ApiException(400, REPORT.CANNOT_REPORT_OWN_CONTENT);
      }

      // Check if user has already reported this contribution
      const hasReported = await contributionReportRepository.hasUserReported(
        contributionId,
        userId
      );
      if (hasReported) {
        throw new ApiException(400, REPORT.ALREADY_REPORTED);
      }

      // Create the report
      const report = await contributionReportRepository.create({
        contributionId,
        reportedBy: userId,
        reason,
      });

      return report;
    } catch (error) {
      throw error;
    }
  },
};

module.exports = reportService;
