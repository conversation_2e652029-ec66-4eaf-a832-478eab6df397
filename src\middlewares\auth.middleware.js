/**
 * Authentication and Authorization Middleware
 */
const jwt = require('jsonwebtoken');
const { ApiException } = require('@utils/exception.utils');
const databaseService = require('@config/database.config');
const { UserRole } = require('@utils/enums.utils');
const { AUTH } = require('@utils/messages.utils');

/**
 * Admin authenticate middleware
 * Verifies JWT token and attaches admin to request
 */
const authenticateAdmin = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader?.startsWith('Bearer ')) {
      throw new ApiException(401, AUTH.AUTHENTICATION_FAILED);
    }

    const token = authHeader.split(' ')[1];
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    try {
      const Admin = databaseService.getAdminModel();
      const admin = await Admin.findByPk(decoded.id);

      if (!admin) {
        throw new ApiException(401, AUTH.INVALID_TOKEN);
      }

      // Use the role from the decoded token
      req.user = {
        id: admin.id,
        email: admin.email,
        role: decoded.role || UserRole.ADMIN, // Fallback to admin role if not in token
      };
    } catch (error) {
      if (error instanceof ApiException) {
        throw error;
      }
      throw new ApiException(401, AUTH.AUTHENTICATION_FAILED);
    }

    next();
  } catch (error) {
    if (
      error.name === 'JsonWebTokenError' ||
      error.name === 'TokenExpiredError'
    ) {
      return next(new ApiException(401, AUTH.INVALID_TOKEN));
    }
    next(error);
  }
};

/**
 * User authenticate middleware
 * Verifies JWT token and attaches user to request
 */
const authenticateUser = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader?.startsWith('Bearer ')) {
      throw new ApiException(401, AUTH.AUTHENTICATION_FAILED);
    }

    const token = authHeader.split(' ')[1];
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    try {
      const User = databaseService.getUserModel();
      const user = await User.findByPk(decoded.id);

      if (!user) {
        throw new ApiException(401, AUTH.INVALID_TOKEN);
      }

      // Attach user to request
      req.user = {
        id: user.id,
        email: user.email,
        userType: user.userType,
        role: UserRole.USER,
      };
    } catch (error) {
      if (error instanceof ApiException) {
        throw error;
      }
      throw new ApiException(401, AUTH.AUTHENTICATION_FAILED);
    }

    next();
  } catch (error) {
    if (
      error.name === 'JsonWebTokenError' ||
      error.name === 'TokenExpiredError'
    ) {
      return next(new ApiException(401, AUTH.INVALID_TOKEN));
    }
    next(error);
  }
};

/**
 * Generic authenticate middleware
 * Determines the type of user from the token and routes to the appropriate authentication middleware
 */
const authenticate = async (req, res, next) => {
  try {
    // if (
    //   (req.path.startsWith('/admin/login') && req.method === 'POST') ||
    //   (req.path.startsWith('/user/login') && req.method === 'POST') ||
    //   (req.path.startsWith('/user/register') && req.method === 'POST') ||
    //   req.path === '/health'
    // ) {
    //   return next();
    // }

    const authHeader = req.headers.authorization;

    if (!authHeader?.startsWith('Bearer ')) {
      throw new ApiException(401, AUTH.AUTHENTICATION_FAILED);
    }

    const token = authHeader.split(' ')[1];
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Check if the user's role matches the route they're trying to access
    const isAdminPath = req.originalUrl.startsWith('/admin');
    const isUserPath = req.originalUrl.startsWith('/user');

    if (isAdminPath && decoded.role !== UserRole.ADMIN) {
      throw new ApiException(401, AUTH.UNAUTHORIZED);
    }

    if (isUserPath && decoded.role !== UserRole.USER) {
      throw new ApiException(401, AUTH.UNAUTHORIZED);
    }

    // Continue with appropriate authentication based on role
    if (decoded.role === UserRole.ADMIN) {
      return authenticateAdmin(req, res, next);
    } else {
      return authenticateUser(req, res, next);
    }
  } catch (error) {
    if (
      error.name === 'JsonWebTokenError' ||
      error.name === 'TokenExpiredError'
    ) {
      return next(new ApiException(401, AUTH.INVALID_TOKEN));
    }
    next(error);
  }
};

/**
 * Authorize middleware
 * Checks if user has required role
 * @param {Array} roles - Array of allowed roles
 */
const authorize = (roles = []) => {
  return (req, res, next) => {
    try {
      if (!req.user) {
        throw new ApiException(401, AUTH.AUTHENTICATION_FAILED);
      }

      // Convert string role to array if needed
      const allowedRoles = Array.isArray(roles) ? roles : [roles];

      // If no roles specified, allow all authenticated users
      if (allowedRoles.length === 0) {
        return next();
      }

      // Check if user's role is in the allowed roles
      if (!allowedRoles.includes(req.user.role)) {
        throw new ApiException(403, AUTH.FORBIDDEN);
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

module.exports = {
  authenticate,
  authenticateAdmin,
  authenticateUser,
  authorize,
};
