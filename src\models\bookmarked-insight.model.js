/**
 * BookmarkedInsight Model
 * Represents the many-to-many relationship between User and Insight for bookmarks
 */
const { Model, DataTypes } = require('sequelize');

class BookmarkedInsight extends Model {
  static init(sequelize) {
    super.init(
      {
        id: {
          type: DataTypes.UUID,
          defaultValue: DataTypes.UUIDV4,
          primaryKey: true,
        },
        userId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'User',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
        insightId: {
          type: DataTypes.UUID,
          allowNull: false,
          references: {
            model: 'Insight',
            key: 'id',
          },
          onDelete: 'CASCADE',
        },
      },
      {
        sequelize,
        modelName: 'BookmarkedInsight',
        tableName: 'BookmarkedInsight',
        timestamps: true,
        indexes: [
          {
            unique: true,
            fields: ['userId', 'insightId'],
            name: 'bookmarked_insight_unique_idx',
          },
        ],
      }
    );
  }

  static associate(models) {
    // No direct associations needed as this is a junction table
    // The associations are defined in the User and Insight models
  }
}

module.exports = BookmarkedInsight;
