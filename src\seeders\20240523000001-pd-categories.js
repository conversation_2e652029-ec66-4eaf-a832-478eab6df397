'use strict';
const { v4: uuidv4 } = require('uuid');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Check if categories already exist
    const existingCategories = await queryInterface.sequelize.query(
      'SELECT LOWER(name) as lowercase_name FROM "PdCategory"',
      { type: Sequelize.QueryTypes.SELECT }
    );

    const existingLowercaseNames = existingCategories.map(
      (category) => category.lowercase_name
    );

    // Define categories data with proper capitalization and different timestamps
    // Create a base date and then add different offsets for each record
    const baseDate = new Date();

    const categories = [
      {
        id: uuidv4(),
        name: 'Classroom Management',
        createdAt: new Date(baseDate.getTime()),
        updatedAt: new Date(baseDate.getTime()),
      },
      {
        id: uuidv4(),
        name: 'Student Well-Being',
        createdAt: new Date(baseDate.getTime() + 1000), // 1 second later
        updatedAt: new Date(baseDate.getTime() + 1000),
      },
      {
        id: uuidv4(),
        name: 'Educator Well-Being',
        createdAt: new Date(baseDate.getTime() + 2000), // 2 seconds later
        updatedAt: new Date(baseDate.getTime() + 2000),
      },
      {
        id: uuidv4(),
        name: 'Literacy',
        createdAt: new Date(baseDate.getTime() + 3000), // 3 seconds later
        updatedAt: new Date(baseDate.getTime() + 3000),
      },
      {
        id: uuidv4(),
        name: 'Multiple Language Learners',
        createdAt: new Date(baseDate.getTime() + 4000), // 4 seconds later
        updatedAt: new Date(baseDate.getTime() + 4000),
      },
      {
        id: uuidv4(),
        name: 'Co-Teaching & Collaboration',
        createdAt: new Date(baseDate.getTime() + 5000), // 5 seconds later
        updatedAt: new Date(baseDate.getTime() + 5000),
      },
      {
        id: uuidv4(),
        name: 'Effective Group Work',
        createdAt: new Date(baseDate.getTime() + 6000), // 6 seconds later
        updatedAt: new Date(baseDate.getTime() + 6000),
      },
      {
        id: uuidv4(),
        name: 'Student Engagement',
        createdAt: new Date(baseDate.getTime() + 7000), // 7 seconds later
        updatedAt: new Date(baseDate.getTime() + 7000),
      },
      {
        id: uuidv4(),
        name: 'Assessment Best Practices',
        createdAt: new Date(baseDate.getTime() + 8000), // 8 seconds later
        updatedAt: new Date(baseDate.getTime() + 8000),
      },
      {
        id: uuidv4(),
        name: 'Parent Communication',
        createdAt: new Date(baseDate.getTime() + 9000), // 9 seconds later
        updatedAt: new Date(baseDate.getTime() + 9000),
      },
      {
        id: uuidv4(),
        name: 'Differentiation',
        createdAt: new Date(baseDate.getTime() + 10000), // 10 seconds later
        updatedAt: new Date(baseDate.getTime() + 10000),
      },
      {
        id: uuidv4(),
        name: 'Technology',
        createdAt: new Date(baseDate.getTime() + 11000), // 11 seconds later
        updatedAt: new Date(baseDate.getTime() + 11000),
      },
    ];

    // Filter out categories that already exist (case-insensitive comparison)
    // but preserve the original case when inserting
    const newCategories = categories.filter(
      (category) =>
        !existingLowercaseNames.includes(category.name.toLowerCase())
    );

    if (newCategories.length > 0) {
      await queryInterface.bulkInsert('PdCategory', newCategories);
      console.log(`${newCategories.length} PD categories created successfully`);
    } else {
      console.log('All PD categories already exist, skipping creation');
    }
  },

  async down(queryInterface, Sequelize) {
    // Define the names of the categories to be removed
    const names = [
      'Classroom Management',
      'Student Well-Being',
      'Educator Well-Being',
      'Literacy',
      'Multiple Language Learners',
      'Co-Teaching & Collaboration',
      'Effective Group Work',
      'Student Engagement',
      'Assessment Best Practices',
      'Parent Communication',
      'Differentiation',
      'Technology',
    ];

    // Delete the categories (case-insensitive)
    for (const name of names) {
      await queryInterface.sequelize.query(
        `
        DELETE FROM "PdCategory"
        WHERE LOWER(name) = LOWER(:name)
      `,
        {
          replacements: { name },
          type: Sequelize.QueryTypes.DELETE,
        }
      );
    }

    console.log('PD categories removed successfully');
  },
};
